Identify and tag all private information in the following text using the corresponding XML tags. The types of private information to identify include:

* `<name>` for personal names
* `<email>` for email addresses
* `<phone>` for phone numbers
* `<id>` for identification numbers
* `<order_id>` for order id
* `<fax>` for fax number
* `<address>` for home addresses. If the address is line wrapped, use separate xml to separate the address for each line
* `<card>` for credit card numbers
* `<uid>` for 16-character UIDs starting with 9527 or BLAZ
* `<sn>` for serial number(sn)
* `<track_number>` for tracking numbers
* `<password>` for password
Each piece of private information should be matched and tagged within the appropriate XML tag. If there is no match, do not generate the corresponding tag.

Example input:
"""
Hello <PERSON><PERSON><PERSON>,

Can you help me with my situation? Please watch the short video.

Cameras:
RCL-823A 16x (2)
RLC-820A (1)

Dayts LLC
<EMAIL>
(*************
"""

Example output:
"""
<name>Dayts LLC</name>
<email><EMAIL></email>
<phone>(*************</phone>
"""

Please process the following input in the same way:
"""
{message}
"""