Objective: To protect data privacy, please anonymize the provided text. This involves replacing specific types of sensitive information found in the text with corresponding XML tags. This process is solely for masking data to prevent the exposure of personal information. The original sensitive information must never appear in the output.

Instructions:
Analyze the input text ({message}) and replace any text matching the following data types with their designated XML tag:

*   Personal Names: Replace with `<name>`
*   Email Addresses: Replace with `<email>`
*   Phone Numbers: Replace with `<phone>`
*   Order IDs: Replace with `<order_id>`
*   Fax Numbers: Replace with `<fax>`
*   Street Addresses (if the address spans multiple lines, use a separate tag for each line): Replace with `<address>`
*   Credit Card Numbers: Replace with `<card>`
*   Specific 16-character UIDs (starting with 9527 or BLAZ): Replace with `<uid>`
*   Serial Numbers (indicated by 'sn' or 'serial number'): Replace with `<sn>`
*   Shipment Tracking Numbers: Replace with `<track_number>`
*   Passwords or Security Credentials: Replace with `<password>`

Rules:
1.  Only replace the identified sensitive data with the specified XML tag (e.g., `<name>`, `<email>`).
2.  If multiple lines constitute a single address, use a separate `<address>` tag for each line.
3.  If a piece of text does not match any of the specified patterns, leave it unchanged.
4.  The output should *only* contain the processed text. Do not add any explanations, greetings, apologies, or other conversational text.
5.  Strictly adhere to the output format shown in the example.
6.  If the input message {message} is exactly "✅ confirmed" or "❓needGuidance" (or their direct multilingual equivalents meaning confirmation or need for guidance), output the input message directly without applying any tags or performing desensitization.

Example Input:

Hello Reolink,

My name is John Doe and my <NAME_EMAIL>. My order ID is 12345XYZ. Please ship to 123 Main St, Anytown, CA 90210. My phone is (*************. UID: 9527ABCDEFGH1234. Tracking: 1Z999AA10123456789. My password hint is 'mypet'.

Example Output:

Hello Reolink,

My name is <name> and my email is <email>. My order ID is <order_id>. Please ship to <address>, <address>. My phone is <phone>. UID: <uid>. Tracking: <track_number>. My password hint is '<password>'.

Now, please process the following input text according to these rules:

{message}