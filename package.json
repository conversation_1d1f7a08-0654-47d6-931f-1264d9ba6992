{"name": "service-ai-support", "version": "1.0.0", "description": "ai support for reolink", "main": "", "scripts": {"typecheck": "ottoia clean && tsc --build $(find packages/ -type f -name tsconfig.json -exec dirname {} \\;)", "lint": "eslint --ext ts packages/**/src", "build": "tsc --build $(find packages/ -type f -name tsconfig.json -exec dirname {} \\;)", "rebuild": "ottoia clean && tsc --build $(find packages/ -type f -name tsconfig.json -exec dirname {} \\;)", "build-watch": "ottoia clean && tsc --build -w $(find packages/ -type f -name tsconfig.json -exec dirname {} \\;)", "test": "echo \"Error: no test specified\" && exit 1", "prepare": "([ -x /usr/bin/bash ] && bash ./init.sh) || sh ./init.sh", "ottoia:prepublish": "npm run rebuild && npm run lint"}, "keywords": [], "author": "LIN DARUI <<EMAIL>>", "license": "ISC", "ottoia": {"releases": {"production": {"tag": "latest"}, "development": {"tag": "dev"}}}, "repository": {"type": "git", "url": "git+https://git.develop.reolink.com.cn/reolink-backend/service-ai-support.git"}, "bugs": {"url": "https://git.develop.reolink.com.cn/reolink-backend/service-ai-support/issues"}, "homepage": "https://git.develop.reolink.com.cn/reolink-backend/service-ai-support#readme", "docker": {"repository": "registry.ap-southeast-1.aliyuncs.com/reolink-services/ai-support", "entry": ["node", "/data/node_modules/.bin/reolink", "run", "packages/server", "config.json"]}, "private": true, "devDependencies": {"@commitlint/cli": "^18.2.0", "@commitlint/config-conventional": "^18.1.0", "@litert/ottoia": "^0.4.0", "@reolink-fx/typeorm-model-generator": "^1.0.37", "@reolink/eslint-plugin-general": "^0.4.2", "@types/chai": "^5.0.1", "@types/libsodium-wrappers": "^0.7.14", "@types/node": "^20.11.5", "@types/turndown": "^5.0.5", "@types/xml2js": "^0.4.14", "chai": "^4.3.10", "husky": "^8.0.3", "typescript": "^5.3.3"}, "dependencies": {"@anthropic-ai/bedrock-sdk": "^0.10.4", "@langchain/langgraph": "^0.2.22", "@qdrant/js-client-rest": "^1.11.0", "@reolink-fx/cache.driver.redis": "^1.0.37", "@reolink-fx/cli": "^1.0.37", "@reolink-fx/database.driver.mysql2": "^1.0.37", "@reolink-fx/http": "^1.0.37", "@reolink-fx/log.driver.redis": "^1.0.37", "@reolink-fx/metrics.driver.prometheus.pull": "^1.0.37", "@reolink-fx/orm": "^1.0.37", "@reolink-fx/rpc": "^1.0.37", "@reolink-fx/rpc.all-built-in-plugins": "^1.0.37", "@reolink-fx/service": "^1.0.37", "@reolink-fx/test": "^1.0.37", "@reolink-fx/uuid": "^1.0.37", "@reolink-services/api-gateway": "^1.0.37", "@reolink-services/application-registry": "^0.9.3", "@reolink-services/export-records": "^1.0.9-20240816", "@reolink-services/sse-broker": "^1.1.1", "@reolink-services/zendesk-sop": "^1.1.7", "@reolink/sys.tools.excel2json": "^0.1.5", "cheerio": "^1.0.0", "cron": "^3.1.7", "libsodium": "^0.7.15", "libsodium-wrappers": "^0.7.15", "openai": "^4.47.1", "sunshine-conversations-client": "^14.3.5", "ts-alias-loader": "^0.1.2", "turndown": "^7.2.0", "xml2js": "^0.6.2", "yaml": "^2.3.4"}, "engines": {"node": ">=20.9.0"}}