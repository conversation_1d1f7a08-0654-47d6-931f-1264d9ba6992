import * as App from '@reolink-fx/app';
import * as GatewayTest from '@reolink-services/api-gateway/lib/TestKits';
import * as Config from '@reolink-fx/config';
import * as DI from '@reolink-fx/di';
import * as $I from '@reolink-services/ai-support';
import * as $R from '@reolink-services/ai-support.restful-apis';
import * as $P from '@reolink-services/ai-support.platform-apis';
import * as GatewaySdk from '@reolink-services/api-gateway';
import * as Http from '@reolink-fx/http';
import { assert as Assert } from 'chai';

@App.AppSchema({
    type: 'app',
    name: $I.SERVICE_NAME + 'Test',
})
export default class ServiceAISupportTest extends App.AbstractApplication {

    private readonly _platformRPC = GatewayTest.useTunnelRpcTestClient<$P.IApiSet>({
        'service': $I.SERVICE_NAME
    });

    public async testSop(): Promise<void> {
        await this._testScene();
        await this._testStep();
        await this._testSop();
    }

    protected async _testScene(): Promise<void> {
        // 创建场景
        const createResult = await this._platformRPC.invoke(
            'CreateScene',
            {
                'scene': `测试场景-${Date.now()}`
            }
        );
        Assert.isOk(createResult);

        // 获取场景列表
        const listResult = await this._platformRPC.invoke(
            'GetSceneList',
            {
                'scene': `测试场景`,
                'limit': 10,
                'page': 1
            }
        );
        console.log(listResult);
        Assert.isOk(listResult);
        Assert.isArray(listResult.items);
        Assert.isAtLeast(listResult.items.length, 1);

        const sceneId = listResult.items[0].id;

        // 更新场景
        const updateResult = await this._platformRPC.invoke(
            'UpdateScene',
            {
                'sceneId': sceneId,
                'scene': `更新后的测试场景-${Date.now()}`
            }
        );
        Assert.isOk(updateResult);

        // 删除场景
        const deleteResult = await this._platformRPC.invoke(
            'DeleteScene',
            {
                'sceneId': sceneId
            }
        );
        Assert.isOk(deleteResult);
    }

    protected async _testStep(): Promise<void> {
        // 创建步骤
        const createResult = await this._platformRPC.invoke(
            'CreateStep',
            {
                'step': `测试步骤-${Date.now()}`,
                'options': {
                    'articleId': '123',
                    'description': `这是一个测试步骤的描述-${Date.now()}`
                }
            }
        );
        Assert.isOk(createResult);

        // 获取步骤列表
        const listResult = await this._platformRPC.invoke(
            'GetStepList',
            {
                'step': '测试步骤',
                'limit': 10,
                'page': 1
            }
        );
        Assert.isOk(listResult);
        Assert.isArray(listResult.items);
        Assert.isAtLeast(listResult.items.length, 1);

        const stepId = listResult.items[0].id;

        // 更新步骤
        const updateResult = await this._platformRPC.invoke(
            'UpdateStep',
            {
                'stepId': stepId,
                'step': '更新后的测试步骤',
                'options': {
                    'articleId': '123',
                    'description': '这是更新后的测试步骤描述'
                }
            }
        );
        Assert.isOk(updateResult);

        // 删除步骤
        const deleteResult = await this._platformRPC.invoke(
            'DeleteStep',
            {
                'stepId': stepId
            }
        );
        Assert.isOk(deleteResult);
    }

    protected async _testSop(): Promise<void> {
        // 先创建场景
        const createSceneResult = await this._platformRPC.invoke(
            'CreateScene',
            {
                'scene': `测试SOP场景-${Date.now()}`
            }
        );
        Assert.isOk(createSceneResult);

        // 获取场景ID
        const sceneListResult = await this._platformRPC.invoke(
            'GetSceneList',
            {
                'scene': '测试SOP场景',
                'limit': 10,
                'page': 1
            }
        );
        Assert.isOk(sceneListResult);
        Assert.isArray(sceneListResult.items);
        Assert.isAtLeast(sceneListResult.items.length, 1);
        const sceneId = sceneListResult.items[0].id;

        // 创建步骤
        const createStepResult = await this._platformRPC.invoke(
            'CreateStep',
            {
                'step': `测试SOP步骤-${Date.now()}`,
                'options': {
                    'articleId': '123',
                    'description': `这是一个测试SOP步骤的描述-${Date.now()}`
                }
            }
        );
        Assert.isOk(createStepResult);

        // 获取步骤ID
        const stepListResult = await this._platformRPC.invoke(
            'GetStepList',
            {
                'step': '测试SOP步骤',
                'limit': 10,
                'page': 1
            }
        );
        Assert.isOk(stepListResult);
        Assert.isArray(stepListResult.items);
        Assert.isAtLeast(stepListResult.items.length, 1);
        const stepId = stepListResult.items[0].id;

        // 创建 SOP
        const createResult = await this._platformRPC.invoke(
            'CreateSop',
            {
                'sceneId': sceneId,
                'tags': ['NVR', 'POE'],
                'intent': `测试SOP意图-${Date.now()}`,
                'description': `这是一个测试SOP的描述-${Date.now()}`,
                'solutions': {
                    'steps': [
                        {
                            'stepId': stepId,
                            'priority': 1
                        }
                    ]
                }
            }
        );
        Assert.isOk(createResult);

        // 获取 SOP 列表
        const listResult = await this._platformRPC.invoke(
            'GetSopList',
            {
                'intent': '测试SOP意图',
                'limit': 10,
                'page': 1
            }
        );
        Assert.isOk(listResult);
        Assert.isArray(listResult.items);
        Assert.isAtLeast(listResult.items.length, 1);

        const sopId = listResult.items[0].id;

        // 更新 SOP
        const updateResult = await this._platformRPC.invoke(
            'UpdateSop',
            {
                'sopId': sopId,
                'intent': `更新后的测试SOP意图-${Date.now()}`,
                'description': `这是更新后的测试SOP描述-${Date.now()}`,
                'tags': ['NVR', 'POE', 'Camera'],
                'solutions': {
                    'steps': [
                        {
                            'stepId': stepId,
                            'priority': 2
                        }
                    ]
                }
            }
        );
        Assert.isOk(updateResult);
    }

    private readonly _restClient = GatewayTest.useRestfulRpcTestClient<$R.IApiSet>({
        'service': $I.SERVICE_NAME
    });

    public async chat(): Promise<void> {

        console.log(await this._restClient.invoke('ChatWithScene',  {
            'body': {
                'raw': JSON.stringify({
                    'scene': 'reolink-zd',
                    'content': '"The ethernet camera area has no power to the cameras everything else works but i cannot connect any poe cameras the lights are not on in that 8 channel port area when i connect anything im thinking it needs to be replaced i purchesed the unit April 22 2022 via amazon',
                    'params': {
                        'slugs': ['troubleshooting', 'rln8-410-rln16-410', 'poe'],
                        'productModel': 'RLN8-410',
                    }
                })
            } as any,
            qs: {},
            path: {}
        }));
    }

    public async troubleshootingflow(): Promise<void> {

        console.log(await this._restClient.invoke('ReceiveMessage',  {
            'body': {
                'raw': JSON.stringify({
                    'app': {
                        'id': '6356055758067700ffccfd54'
                    },
                    'webhook': {
                        'id': '675114d829187d0403628ee5',
                        'version': 'v2'
                    },
                    'events': [
                        {
                            'id': '67c95c525f680c5fd43245e9',
                            'createdAt': '2025-03-06T08:26:58.777Z',
                            'type': 'conversation:message',
                            'payload': {
                                'conversation': {
                                    'id': '67c1230f433386fa96186361',
                                    'type': 'personal',
                                    'brandId': '20671518800281',
                                    'activeSwitchboardIntegration': {
                                        'id': '6422cf3e7949b10112d91eed',
                                        'name': 'zd-answerBot',
                                        'integrationId': '6356055836088d8295067e2b',
                                        'integrationType': 'zd:answerBot'
                                    }
                                },
                                'message': {
                                    'id': '67c95c5279ef8f8e189815b4',
                                    'received': '2025-03-06T08:26:58.777Z',
                                    'author': {
                                        'userId': '67c1230f433386fa9618635c',
                                        'displayName': 'Web User 67c1230f433386fa9618635c',
                                        'type': 'user',
                                        'user': {
                                            'id': '67c1230f433386fa9618635c',
                                            'profile': {
                                                'locale': 'zh-CN',
                                                'localeOrigin': 'apiRequest'
                                            },
                                            'signedUpAt': '2025-02-28T02:44:31.783Z',
                                            'metadata': {},
                                            'identities': []
                                        }
                                    },
                                    'content': {
                                        'type': 'text',
                                        'text': '我的摄像头没办法充电了,你们的产品质量实在是太差了！！！'
                                    },
                                    'source': {
                                        'integrationId': '65b487193228a956f1bb58eb',
                                        'type': 'web',
                                        'device': {
                                            'id': '67c1230f433386fa9618635e',
                                            'guid': '753fa3961e0f4b7fa43637f6999507fb',
                                            'clientId': '67c1230f433386fa9618635d',
                                            'integrationId': '65b487193228a956f1bb58eb',
                                            'type': 'web',
                                            'status': 'active',
                                            'info': {
                                                'vendor': 'zendesk',
                                                'sdkVersion': '0.1',
                                                'URL': '10rd.club',
                                                'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                                                'referrer': '',
                                                'browserLanguage': 'zh-CN',
                                                'currentUrl': 'https://10rd.club/smooch/t1.html',
                                                'currentTitle': 'AI Smooch',
                                                'ipAddress': '*************',
                                                'country': 'China',
                                                'countryCode': 'CN',
                                                'state': 'Guangdong',
                                                'stateCode': 'GD',
                                                'city': 'Shenzhen'
                                            },
                                            'lastSeen': '2025-03-06T08:26:58.783Z'
                                        }
                                    }
                                }
                            }
                        }
                    ]
                })
            } as any,
            qs: {},
            path: {}
        }));
    }

    public async detectSlug(): Promise<void> {

        console.log(await this._restClient.invoke('GetAggregationPageSlug',  {
            'body': {
                'raw': JSON.stringify({
                    'productModel': 'RLN8-410',
                    'content': `Hello Team,
I received and installed the cam, but the audio recording is not working. When i try to enable it, i receive the following message. "An error occurred. Retry later". 
Can you please help me resolve it or help me initiate a return.`
                })
            } as any,
            qs: {},
            path: {}
        }));
    }

    private _requestApi(
        args: Record<string, any>,
    ): Promise<Http.IHttpResponse<Record<string, any>>> {

        return Http.requestAsJson({
            url: `https://apis.reolink.dev/v2/app/startup`,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-client-id': 'REO-aPOx]dxqdnaWBChRZprp',
            },
            data: JSON.stringify(GatewaySdk.encodeStmV1(
                'APP_START_UP_QUERY',
                Date.now().toString(),
                args,
            )),
            maxResponseSize: 1024 * 1024 * 10,
        }) as Promise<Http.IHttpResponse<Record<string, any>>>;
    }

    public async testStartup(): Promise<void> {

        const result = await this._requestApi({
            'clientVersion': '4.51.0',
            'devices': [
                {
                    'm': 'RLN16-410',
                    'u': '952700Y0046U3YGK'
                }
            ],
            'fid': {},
            'iid': '72d23616-cf14-4f02-8db9-c8be861a3346-7258087297051007695',
            'lang': 'en',
            'region': 'CN',
            'tz': '+0800'
        });

        console.log(result.data);
        console.log(JSON.stringify(result.data));
    }

    public async main(): Promise<number> {
        await this.testStartup();
        return 0;
    }

    public static async onInit(ctr: DI.IContainer): Promise<void> {

        (await Config.getManager(ctr)).load(`${__dirname}/../config.json`);
    }
}
