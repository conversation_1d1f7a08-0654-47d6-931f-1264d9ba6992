import * as Test from '@reolink-fx/test';
import * as $P from '@reolink-services/ai-support.platform-apis';
import * as GatewayTest from '@reolink-services/api-gateway/lib/TestKits';
// import * as iAG from '@reolink-services/api-gateway/lib/Internal';
import * as $AISupport from '@reolink-services/ai-support';
import { assert as Assert } from 'chai';

@Test.Module({
    name: 'SopTest'
})
export default class Sop {

    private readonly _platformRPC = GatewayTest.useTunnelRpcTestClient<$P.IApiSet>({
        'service': $AISupport.SERVICE_NAME
    });

    @Test.Case({
        'name': 'SopTest'
    })
    public async testSop(): Promise<void> {
        await this._testScene();
        await this._testStep();
    }

    protected async _testScene(): Promise<void> {
        // 创建场景
        const createResult = await this._platformRPC.invoke(
            'CreateScene',
            {
                'scene': '测试场景'
            }
        );
        Assert.isOk(createResult);

        // 获取场景列表
        const listResult = await this._platformRPC.invoke(
            'GetSceneList',
            {
                'scene': '测试场景',
                'limit': 10,
                'page': 1
            }
        );
        Assert.isOk(listResult);
        Assert.isArray(listResult.items);
        Assert.isAtLeast(listResult.items.length, 1);

        const sceneId = listResult.items[0].id;

        // 更新场景
        const updateResult = await this._platformRPC.invoke(
            'UpdateScene',
            {
                'sceneId': sceneId,
                'scene': '更新后的测试场景'
            }
        );
        Assert.isOk(updateResult);

        // 删除场景
        const deleteResult = await this._platformRPC.invoke(
            'DeleteScene',
            {
                'sceneId': sceneId
            }
        );
        Assert.isOk(deleteResult);
    }

    protected async _testStep(): Promise<void> {
        // 创建步骤
        const createResult = await this._platformRPC.invoke(
            'CreateStep',
            {
                'step': '测试步骤',
                'options': {
                    'articleId': '123',
                    'description': '这是一个测试步骤的描述'
                }
            }
        );
        Assert.isOk(createResult);

        // 获取步骤列表
        const listResult = await this._platformRPC.invoke(
            'GetStepList',
            {
                'step': '测试步骤',
                'limit': 10,
                'page': 1
            }
        );
        Assert.isOk(listResult);
        Assert.isArray(listResult.items);
        Assert.isAtLeast(listResult.items.length, 1);

        const stepId = listResult.items[0].id;

        // 更新步骤
        const updateResult = await this._platformRPC.invoke(
            'UpdateStep',
            {
                'stepId': stepId,
                'step': '更新后的测试步骤',
                'options': {
                    'articleId': '123',
                    'description': '这是更新后的测试步骤描述'
                }
            }
        );
        Assert.isOk(updateResult);

        // 删除步骤
        const deleteResult = await this._platformRPC.invoke(
            'DeleteStep',
            {
                'stepId': stepId
            }
        );
        Assert.isOk(deleteResult);
    }
}
