import { IOptions } from './Step';
import * as GatewaySdk from '@reolink-services/api-gateway';
import * as Rpc from '@reolink-fx/rpc';
export interface ICreateSopArgs {

    /**
     * 场景id
     *
     * uint_64
     */
    sceneId: string;

    /**
     * 产品分类
     */
    tags: string[];

    /**
     * 意图
     *
     * 取值范围：1 ~ 255字符
     */
    intent: string;

    /**
     * 意图描述
     *
     * 取值范围：1 ~ 1000字符
     */
    description: string;

    /**
     * 解决方案
     */
    solutions: ISolution;
}

export interface IDeleteSopArgs {

    /**
     * id
     *
     * uint_64
     */
    sopId: string;
}

export interface IGetSopListArgs {

    /**
     * id列表
     */
    ids?: string[];

    /**
     * 意图
     *
     * 取值范围: 1-255 个字符
     */
    intent?: string;

    /**
     * 产品分类
     */
    tag?: string;

    /**
     * 返回多少条记录
     * 可选范围为 1-1000
     */
    limit: number;

    /**
     * 分页页数
     * 正整数从1开始
     */
    page: number;
}

export interface IUpdateSopArgs {

    /**
     * id
     * uint
     */
    sopId: string;

    /**
     * 场景id
     * uint
     */
    sceneId?: string;

    /**
     * 产品分类
     */
    tags?: string[];

    /**
     * 意图
     *
     * 取值范围：1 ~ 255字符
     */
    intent?: string;

    /**
     * 意图描述
     *
     * 取值范围：1 ~ 1000字符
     */
    description?: string;

    /**
     * 解决方案
     */
    solutions?: ISolution;
}

interface ISolution {

    /**
     * 解决步骤
     */
    steps: Array<{
        /**
         * 步骤ID
         */
        stepId: string;

        /**
         * 优先级
         */
        priority: number;
    }>;

}

export interface IViewSolution {

    /**
     * 解决步骤
     */
    steps: Array<{

        /**
         * 步骤ID
         */
        stepId: string;

        /**
         * 步骤
         *
         * 取值范围: 1 - 1024 个字符
         */
        step: string;

        /**
         * 帮助指引
         */
        options: IOptions;

        /**
         * 优先级
         */
        priority: number;
    }>;

}

export interface IViewSop {

    /**
     * SOP ID
     *
     * 取值范围: safe_uint
     */
    id: string;

    /**
     * 场景名称
     * 取值范围: 1-255 个字符
     */
    scene: string;

    /**
     * 产品分类
     */
    tags: string[];

    /**
     * 意图
     */
    intent: string;

    /**
     * 意图描述
     */
    description: string;

    /**
     * 解决方案
     */
    solutions: IViewSolution;

    /**
     * 创建时间(毫秒时间戳)
     *
     * 取值范围: safe_uint
     */
    createdAt: number;

    /**
     * 创建人
     *
     * 取值范围: safe_uint
     */
    createdBy: string;

    /**
     * 修改时间(毫秒时间戳)
     *
     * 取值范围: safe_uint
     */
    modifiedAt: number;

    /**
     * 修改人
     *
     * 取值范围: safe_uint
     */
    modifiedBy: string;
}

export const PREM_CREATE_SOP: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'createSop': {
        'action': 'AISupport.CreateSop',
        'resource': 'ai-support:sop'
    }
};

export const PREM_UPDATE_SOP: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'updateSop': {
        'action': 'AISupport.UpdateSop',
        'resource': 'ai-support:sop'
    }
};

export const PREM_DELETE_SOP: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'deleteSop': {
        'action': 'AISupport.DeleteSop',
        'resource': 'ai-support:sop'
    }
};

export const PREM_GET_SOP: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'getSop': {
        'action': 'AISupport.GetSop',
        'resource': 'ai-support:sop'
    }
};

export interface IApiSet {

    createSop(
        args: ICreateSopArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse>;

    deleteSop(
        args: IDeleteSopArgs
    ): Promise<Rpc.IOkResponse>;

    updateSop(
        args: IUpdateSopArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse>;

    getSopList(
        args: IGetSopListArgs
    ): Promise<Rpc.IListResponse<IViewSop>>;
}
