import * as GatewaySdk from '@reolink-services/api-gateway';
import * as Rpc from '@reolink-fx/rpc';

export interface ICreateSceneArgs {

    /**
     * 场景
     *
     * 取值范围: 1 - 1024 个字符
     */
    scene: string;
}

export interface IDeleteSceneArgs {

    /**
     * 场景id
     *
     * uint_64
     */
    sceneId: string;
}

export interface IGetSceneListArgs {

    /**
     * 场景名称
     *
     * 取值范围: 1-1024 个字符
     */
    scene?: string;

    /**
     * 返回多少条记录
     * 可选范围为 1-1000
     */
    limit: number;

    /**
     * 分页页数
     * 正整数从1开始
     */
    page: number;
}

export interface IUpdateSceneArgs {

    /**
     * 场景id
     * uint
     */
    sceneId: string;

    /**
     * 场景
     *
     * 取值范围: 1 - 1024 个字符
     */
    scene: string;
}

export interface IScene {

    /**
     * 向量知识数据库配置 ID
     *
     * 取值范围: safe_uint
     */
    id: string;

    /**
     * 场景
     *
     * 取值范围: 1 - 1024 个字符
     */
    scene: string;

    /**
     * 创建时间(毫秒时间戳)
     *
     * 取值范围: safe_uint
     */
    createdAt: number;

    /**
     * 创建人
     *
     * 取值范围: safe_uint
     */
    createdBy: string;

    /**
     * 修改时间(毫秒时间戳)
     *
     * 取值范围: safe_uint
     */
    modifiedAt: number;

    /**
     * 修改人
     *
     * 取值范围: safe_uint
     */
    modifiedBy: string;
}

export const PREM_CREATE_SCENE: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'createScene': {
        'action': 'AISupport.CreateScene',
        'resource': 'ai-support:sop'
    }
};

export const PREM_UPDATE_SCENE: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'updateScene': {
        'action': 'AISupport.UpdateScene',
        'resource': 'ai-support:sop'
    }
};

export const PREM_DELETE_SCENE: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'deleteScene': {
        'action': 'AISupport.DeleteScene',
        'resource': 'ai-support:sop'
    }
};

export const PREM_GET_SCENE: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'getScene': {
        'action': 'AISupport.GetScene',
        'resource': 'ai-support:sop'
    }
};

export interface IApiSet {

    createScene(
        args: ICreateSceneArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse>;

    deleteScene(
        args: IDeleteSceneArgs
    ): Promise<Rpc.IOkResponse>;

    updateScene(
        args: IUpdateSceneArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse>;

    getSceneList(
        args: IGetSceneListArgs
    ): Promise<Rpc.IListResponse<IScene>>;

}
