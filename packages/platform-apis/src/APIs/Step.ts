import * as GatewaySdk from '@reolink-services/api-gateway';
import * as Rpc from '@reolink-fx/rpc';

export interface ICreateStepArgs {

    /**
     * 步骤
     *
     * 取值范围: 1 - 512 个字符
     */
    step: string;

    /**
     * 帮助指引
     */
    options: IOptions;
}

export interface IDeleteStepArgs {

    /**
     * 步骤id
     *
     * uint_64
     */
    stepId: string;
}

export interface IUpdateStepArgs {

    /**
     * 步骤id
     * uint
     */
    stepId: string;

    /**
     * 步骤
     *
     * 取值范围: 1 - 512 个字符
     */
    step: string;

    /**
     * 帮助指引
     */
    options: IOptions;
}

export interface IOptions {

    /**
     * 文章ID
     */
    articleId?: string;

    /**
     * 操作指引
     */
    description?: string;
}

export interface IGetStepListArgs {

    /**
     * 步骤名称
     *
     * 取值范围: 1-512 个字符
     */
    step?: string;

    /**
     * 返回多少条记录
     * 可选范围为 1-1000
     */
    limit: number;

    /**
     * 分页页数
     * 正整数从1开始
     */
    page: number;
}

export interface IStep {

    /**
     * 步骤 ID
     *
     * 取值范围: safe_uint
     */
    id: string;

    /**
     * 步骤
     *
     * 取值范围: 1 - 255 个字符
     */
    step: string;

    /**
     * 帮助指引
     */
    options: IOptions;

    /**
     * 创建时间(毫秒时间戳)
     *
     * 取值范围: safe_uint
     */
    createdAt: number;

    /**
     * 创建人
     *
     * 取值范围: safe_uint
     */
    createdBy: string;

    /**
     * 修改时间(毫秒时间戳)
     *
     * 取值范围: safe_uint
     */
    modifiedAt: number;

    /**
     * 修改人
     *
     * 取值范围: safe_uint
     */
    modifiedBy: string;
}

export const PREM_CREATE_STEP: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'createStep': {
        'action': 'AISupport.CreateStep',
        'resource': 'ai-support:sop'
    }
};

export const PREM_UPDATE_STEP: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'updateStep': {
        'action': 'AISupport.UpdateStep',
        'resource': 'ai-support:sop'
    }
};

export const PREM_DELETE_STEP: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'deleteStep': {
        'action': 'AISupport.DeleteStep',
        'resource': 'ai-support:sop'
    }
};

export const PREM_GET_STEP: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'getStep': {
        'action': 'AISupport.GetStep',
        'resource': 'ai-support:sop'
    }
};

export interface IApiSet {

    createStep(
        args: ICreateStepArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse>;

    deleteStep(
        args: IDeleteStepArgs
    ): Promise<Rpc.IOkResponse>;

    updateStep(
        args: IUpdateStepArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse>;

    getStepList(
        args: IGetStepListArgs
    ): Promise<Rpc.IListResponse<IStep>>;

}
