import * as GatewaySdk from '@reolink-services/api-gateway';
import * as Rpc from '@reolink-fx/rpc';
export interface IGetConversationListArgs {

    /**
     * 开始时间
     *
     * 取值范围: safe_uint
     */
    startTime?: number;

    /**
     * 结束时间
     *
     * 取值范围: safe_uint
     */
    endTime?: number;

    /**
     * 问题分类 其它 咨询类 排查类
     */
    category?: 'other' | 'consultation' | 'troubleshooting';

    /**
     * 最后识别的情绪
     */
    emotion?: 'neutral' | 'negative' | 'positive' | 'extremely_negative';

    /**
     * 问题是否得到解决
     *
     */
    issueResolved?: boolean;

    /**
     * 是否是错误意图（可标记）
     */
    isWrongIntent?: boolean;

    /**
     * 是否转人工
     */
    isPassToAgent?: boolean;

    /**
     * 是否创建工单
     */
    isCreateTicket?: boolean;

    /**
     * 最后应用的sopid
     */
    sopId?: string;

    /**
     * 返回多少条记录
     * 可选范围为 1 - 1000
     */
    limit: number;

    /**
     * 分页页数
     * 正整数从 1 开始
     */
    page: number;
}

export type TUnresolvedReason = 'too_much_irrelevant_information' | 'not_consistent_with_the_facts'
| 'provided_step_cannot_be_completed' | 'waiting_time_is_too_long' | 'page_interaction' | 'other';

export interface IConversation {

    /**
     * 会话Id
     *
     * 取值范围: safe_uint
     */
    id: string;

    /**
     * 用户Id，未登录为0
     *
     * 取值范围: safe_uint
     */
    userId: string;

    /**
     * ZD用户ID
     */
    zdUserId: string;

    /**
     * 最后识别的意图
     */
    intent: string;

    /**
     * 问题分类 其它 咨询类 排查类
     */
    category: 'other' | 'consultation' | 'troubleshooting';

    /**
     * 最后识别的情绪
     */
    emotion: 'neutral' | 'negative' | 'positive' | 'extremely_negative';

    /**
     * 是否为内部测试
     */
    isInternalTest: boolean;

    /**
     * 是否是错误意图（可标记）
     */
    isWrongIntent: boolean;

    /**
     * 客服评论
     *
     * 取值范围: 1 - 255 个字符
     */
    agentComments: string;

    /**
     * 是否转人工
     */
    isPassToAgent: boolean;

    /**
     * 是否创建工单
     */
    isCreateTicket: boolean;

    /**
     * 最后应用的sopid
     */
    sopId: string;

    /**
     * 最后推送的步骤id
     */
    lastStepId: string;

    /**
     * 已推送的SOP步骤
     */
    pushedSteps: Array<{

        sopId: string;

        /**
         * 意图
         */
        intent: string;

        /**
         * 意图完成度
         */
        completionPercentage?: number;

        steps: Array<{

            /**
             * 步骤id
             */
            stepId: string;

            /**
             * 步骤
             */
            step: string;

            /**
             * 是否推送提示
             */
            isPushTip: boolean;
        }>;
    }>;

    /**
     * 问题是否得到解决
     *
     */
    issueResolved: boolean;

    /**
     * 未解决问题的原因
     * too_much_irrelevant_information: 太多不相关信息   not_consistent_with_the_facts: 与事实不一致
     * provided_step_cannot_be_completed: 无法完成给出的步骤  waiting_time_is_too_long: 等待时间过长  other: 其他
     */
    problemUnresolvedReasons?: TUnresolvedReason[];

    /**
     * 额外评论
     *
     * 取值范围: 1 - 255 个字符
     */
    additionalComments: string;

    /**
     * 反馈时间
     *
     * 取值范围: safe_uint
     */
    feedbackCreatedAt: number;

    /**
     * 会话信息
     *
     * 取值范围: 1 - 65535 个字符
     */
    messages: Array<{

        /**
         * 消息发送者
         *
         * 取值范围: user: 用户, assistant: 助手
         */
        role: 'user' | 'assistant';

        /**
         * 内容
         *
         */
        content: string;

        /**
         * 创建时间
         *
         * 取值范围: safe_uint
         */
        createdAt: number;

    }>;

    /**
     * 创建时间
     *
     * 取值范围: safe_uint
     */
    createdAt: number;
}

export interface IMarkConversationArgs {

    /**
     * 会话Id
     *
     * 取值范围: safe_uint
     */
    id: string;

    /**
     * 是否是错误意图（可标记）
     */
    isWrongIntent?: boolean;

    /**
     * 客服tag (方便BI分析使用)
     *
     */
    agentTags?: string[];

    /**
     * 客服评论
     *
     * 取值范围: 1 - 255 个字符
     */
    agentComments?: string;

}

export const PREM_GET_CONVERSATION: Record<string, GatewaySdk.ITunnelApiPermission> = {
    'getConversation': {
        'action': 'AISupport.GetConversation',
        'resource': 'ai-support:conversation'
    }
};

export interface IApiSet {

    getConversationList(
        args: IGetConversationListArgs
    ): Promise<Rpc.IListResponse<IConversation>>;

    markConversation(
        args: IMarkConversationArgs
    ): Promise<Rpc.IOkResponse>;

}
