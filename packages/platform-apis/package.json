{"name": "@reolink-services/ai-support.platform-apis", "version": "0.1.0", "main": "lib/index.js", "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "description": "", "scripts": {"ottoia:clean": "rm -rf lib app tsconfig.tsbuildinfo", "prepublishOnly": "echo \"Please use ottoia publishing this package!\"; exit 1;"}, "author": "LIN DARUI <<EMAIL>>", "license": "ISC", "repository": {"type": "git", "url": "git+https://git.develop.reolink.com.cn/reolink-backend/service-ai-support.git"}, "bugs": {"url": "https://git.develop.reolink.com.cn/reolink-backend/service-ai-support/issues"}, "homepage": "https://git.develop.reolink.com.cn/reolink-backend/service-ai-support#readme", "private": true, "dependencies": {"@reolink-fx/rpc": "-"}, "devDependencies": {}, "peerDependencies": {}, "ottoia:alias": "platform-apis", "access": "public", "engines": {"node": ">=20.0.0"}}