export interface IChatWithSceneArgs {

    /**
     * 场景 key
     * （控制请求不同的提示语）
     *
     * 取值范围: 1-32 个字符
     */
    scene: string;

    /**
     * 提问内容
     *
     * 取值范围: 1-5000 个字符
     */
    content: string;

    otherInfo?: Record<string, string>;

}

export interface IChatWithSceneResponse {

    /**
     * ai回复
     *
     */
    response: string;
}

export interface IApiSet {

    chatWithScene(
        args: IChatWithSceneArgs
    ): IChatWithSceneResponse;

}
