$[[extends]]: /opt/reolink/config/rpc-service.yml
application:
  instanceId: c8dfff02-5235-499c-bc60-b7897093446c
uuid:
  default:
    driver: snowflake
    options:
      instanceId: 1
      epoch: '2020-01-11T11:11:11+0800'
db:
  default:
    type: typeorm
    options:
      type: mysql
      host: mysql.reolink.component
      port: 3306
      username: dev_us_ai_support_worker
      password: iNQ4VVju2AfN9w4DKoPk
      database: dev_us_ai_support
      daoRootDir: ./app/DAO
      synchronize: false
      logging: false
      extra:
        connectionLimit: 2
cache:
  zones:
    default:
      driver: redis
      connection: default
      options:
        keyNamespace: 缓存在 Redis 中的 key 的命名前缀，可留空
        notFoundMark: 当一个资源被标记为不存在时，在缓存中的记录值，默认为"-"。
  connections:
    default:
      driver: redis
      password: ""
      database: 9
      host: redis.reolink.component
      port: 6379
sceneConfig:
  reolink-zd: true
promptPath: /home/<USER>/disk/projects/ai-support-project
llmClientOptions:
  clientType: azure
  claude:
    model: anthropic.claude-3-5-sonnet-20240620-v1:0
    maxTokens: 1024
  openai:
    apiKey: xxx
    url: https://open.bigmodel.cn/api/paas/v4/
    chatModel: glm-4-air
    embeddingModel: embedding-2
  azure:
    deployment: xxx
    embeddingDeployment: xxx
    apiVersion: 2024-02-01
    apiKey: xxx
    endpoint: https://reolink-openai.openai.azure.com/
  llmPrice:
    inputPrice: 0.0004
    outputPrice: 0.0004
serverRoot: https://support.reolink.com/
supportResourceHost: apis.reolink.com
updateCron:
  cronConfig: 0 0 * * *
  startRun: false
  enable: false
requestLimit：
  enabled: true
  maxRequestsPerDay: 100
  whiteList:
    - 0
zdModelMapPath: /home/<USER>/projects/svc-ai-support/doc/prompt/detect_done_step
sunshineConversationClientOptions:
  baseUrl: xxx
  keyId: xxx
  secret: xxx
  avatarUrl: xxx
  displayName: xxx
conversationHandlerOptions:
  saveInterval: 1000
encryption:
  publicKeyPath: /data/encrypt/public_key.pem
  privateKeyPath: /data/encrypt/private_key.pem
export:
  path: /data/export
  maximum: 20000
inactiveSessionChecker:
  checkInterval: 300
  inactiveTimeout: 10
  enabled: true
  appId: 6356055758067700ffccfd54