{
  "extends": "../../tsconfig.base.json",
  "compilerOptions": {
    "composite": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "rootDir": "src",
    "outDir": "app",
    "baseUrl": "./src/",
    "paths": {
      "#/*": [
        "./*"
      ]
    },
  },
  "references": [
    { "path": "../schema" },
    { "path": "../platform-apis" },
    { "path": "../restful-apis" },
  ],
  "include": [
    "./src/*.ts",
    "./src/**/*.ts"
  ]
}