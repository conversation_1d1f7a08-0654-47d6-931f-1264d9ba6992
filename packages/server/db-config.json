{"db": {"default": {"type": "typeorm", "options": {"type": "mysql", "host": "localhost", "port": 3306, "username": "root", "password": "password", "database": "gen_base", "logging": false, "extra": {"connectionLimit": 2}}, "generation": {"outDir": "$[[path:src/DAO]]", "tableWhiteList": [], "removeTablePrefix": "", "tableFilterRegExp": ".+", "alwaysGenerateCustomFile": true}}}, "$schema": "../../node_modules/@reolink-fx/config/json-schema.json"}