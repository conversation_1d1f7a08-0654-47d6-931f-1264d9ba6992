{"name": "@reolink-services/ai-support.server", "version": "0.1.0", "main": "app/app.js", "description": "", "scripts": {"ottoia:clean": "rm -rf app tsconfig.tsbuildinfo", "prepublishOnly": "echo \"Please use ottoia publishing this package!\"; exit 1;"}, "author": "LIN DARUI <<EMAIL>>", "license": "ISC", "repository": {"type": "git", "url": "git+https://git.develop.reolink.com.cn/reolink-backend/service-ai-support.git"}, "bugs": {"url": "https://git.develop.reolink.com.cn/reolink-backend/service-ai-support/issues"}, "homepage": "https://git.develop.reolink.com.cn/reolink-backend/service-ai-support#readme", "private": true, "dependencies": {"ts-alias-loader": "-", "@reolink-services/ai-support": "-", "@reolink-services/ai-support.platform-apis": "-", "@reolink-services/ai-support.restful-apis": "-", "@reolink-fx/service": "-"}, "devDependencies": {}, "peerDependencies": {}, "ottoia:alias": "server", "access": "public", "engines": {"node": ">=20.0.0"}}