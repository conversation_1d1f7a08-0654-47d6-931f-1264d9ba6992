import * as Test from '@reolink-fx/test';
import * as DI from '@reolink-fx/di';
import { SyncSOP } from '#/Services/SyncSOP';

@Test.Module({ name: '123' })
export default class TestSyncSOP {

    private readonly _sSop = DI.use(SyncSOP);

    @Test.Case({ name: '1' })
    public async testSyncOnce(): Promise<void> {
        console.log('1');
        const resp = await this._sSop.getSOPs();
        console.log(resp);

    }

    @Test.Case()
    public test(): void {
        console.log(2);
    }
}
