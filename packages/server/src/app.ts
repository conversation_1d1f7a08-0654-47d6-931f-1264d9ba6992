import 'ts-alias-loader';
import * as Svc from '@reolink-fx/service';
import * as $AISupport from '@reolink-services/ai-support';
import * as DI from '@reolink-fx/di';
import { SyncAggregationPage } from './Services/SyncAggregationPage';
import { SyncSOP } from './Services/SyncSOP';
// import { BulkTestChatResponse } from './Services/BulkTestChatResponse';
@Svc.ServiceSchema({
    name: $AISupport.SERVICE_NAME,
})
export default class ServiceAISupport extends Svc.AbstractService {

    private readonly _syncPage = DI.use(SyncAggregationPage);

    private readonly _syncSOP = DI.use(SyncSOP);

    // private readonly _bulkTestIntentDetection = DI.use(BulkTestChatResponse);

    public async main(): Promise<number> {

        await this._start();

        console.log('service started.');

        //await this._bulkTestIntentDetection.bulkTestIntentDetection();

        this._syncPage.syncAggregationPage();

        this._syncSOP.syncSOPVector();

        await this._termination.wait();

        await this._stop();

        return 0;
    }
}
