import * as Config from '@reolink-fx/config';
import * as DAO from '#/DAO';
import * as DI from '@reolink-fx/di';
import * as ORM from '@reolink-fx/orm';
import * as crypto from 'crypto';
import * as _ from '@reolink-fx/utils';
import { E_DECRYPT_FAILED, E_ENCRYPT_FAILED } from '#/Errors/Common';

@DI.Singleton()
export class CryptoUtils {

    private readonly _encryptionRecordsRepo = ORM.useRepository(DAO.EncryptionRecords.Entity);

    private readonly _cryptCfg: {
        publicKeyPath: string;
        privateKeyPath: string;
    } = Config.useConfig({
            path: 'encryption',
            validation: {
                publicKeyPath: 'string(1,)',
                privateKeyPath: 'string(1,)',
            }
        });

    /**
     * 加密文本
     * @param id 加密数据ID
     * @param text 要加密的文本
     * @param conn 数据库连接
     * @returns 加密后的文本（Base64 编码）
     */
    public async encrypt(id: string, text: string, conn?: ORM.ITransactionConnection): Promise<Buffer> {
        try {
            const rsaPub = Buffer.from(await _.File.readTextFile(this._cryptCfg.publicKeyPath));
            const key = crypto.randomBytes(32); // AES-256 需要一个 256 位的密钥

            // 生成随机的初始化向量
            const iv = crypto.randomBytes(16); // 对于 AES，IV 应该是 16 个字节
            // 创建 Cipher 对象
            const cipher1 = crypto.createCipheriv('AES-256-CBC', key, iv);

            let encrypted = cipher1.update(Buffer.from(text));
            encrypted = Buffer.concat([encrypted, cipher1.final()]);

            const rsaPublicKey = crypto.createPublicKey(rsaPub);

            // 使用 RSA 公钥加密 AES 密钥和 IV
            const encryptedAesKey = crypto.publicEncrypt(rsaPublicKey, key);
            const encryptedAesIv = crypto.publicEncrypt(rsaPublicKey, iv);

            const creation: DAO.EncryptionRecords.ICreation = {
                cipherDataId: id,
                cipherSuite: {
                    cipherType: 'aes-256-cbc',
                    encryptedIv: encryptedAesIv.toString('base64'),
                    encryptedSecret: encryptedAesKey.toString('base64'),
                },
                createdAt: Date.now().toString(),
                modifiedAt: Date.now().toString(),
            };

            await DAO.EncryptionRecords.Entity.insert(
                conn ?
                    await conn.getRepository(DAO.EncryptionRecords.Entity) :
                    this._encryptionRecordsRepo,
                creation);

            return encrypted;
        }
        catch (error) {
            throw new E_ENCRYPT_FAILED({ error });
        }
    }

    /**
     * 解密文本
     * @param id 加密数据ID
     * @param encryptedText 要解密的密文（Buffer）
     * @returns 解密后的明文字符串
     */
    public async decrypt(id: string, encryptedText: Buffer): Promise<string> {
        try {
            // 1. 查找加密记录
            const record = await this._encryptionRecordsRepo.findOne({ where: { cipherDataId: id } });
            if (!record) {
                throw new Error(`未找到 ID 为 ${id} 的加密记录`);
            }

            // 2. 读取 RSA 私钥
            const rsaPrivateKeyPem = await _.File.readTextFile(this._cryptCfg.privateKeyPath);
            const rsaPrivateKey = crypto.createPrivateKey(rsaPrivateKeyPem);

            // 3. 解密 AES 密钥和 IV
            const encryptedAesKey = Buffer.from(record.cipherSuite.encryptedSecret, 'base64');
            const encryptedAesIv = Buffer.from(record.cipherSuite.encryptedIv, 'base64');

            const decryptedKey = crypto.privateDecrypt(rsaPrivateKey, encryptedAesKey);
            const decryptedIv = crypto.privateDecrypt(rsaPrivateKey, encryptedAesIv);

            // 4. 创建 Decipher 对象
            const decipher = crypto.createDecipheriv(record.cipherSuite.cipherType, decryptedKey, decryptedIv);

            // 5. 解密文本
            let decrypted = decipher.update(encryptedText);
            decrypted = Buffer.concat([decrypted, decipher.final()]);

            // 6. 返回明文字符串
            return decrypted.toString();
        }
        catch (error) {
            throw new E_DECRYPT_FAILED({ error });
        }
    }
}
