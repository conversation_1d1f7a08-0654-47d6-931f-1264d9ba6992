import { URL } from 'url';
import * as xml2js from 'xml2js';
import { IHeaderMapping } from '../Decls/Types';

export function parseXML(xmlString: string): Record<string, any> {

    const parser = new xml2js.Parser();

    let response = {};

    parser.parseString(xmlString, (error, result) => {
        if (error) {
            console.error(error);
        }

        if (result) {
            response =  result;
        }
    });

    return response;

}

export function getLastPathSegment(url: string): string {
    // 解析 URL
    const parsedUrl = new URL(url);

    // 获取路径部分，并使用 '/' 分割
    const pathSegments = parsedUrl.pathname.split('/');

    // 获取最后一个非空路径段
    const lastSegment = pathSegments.reverse().find(segment => segment) ?? '';

    return lastSegment;
}

// 替换函数
export function replacePlaceholders(template: string, replacements: Record<string, string | number>): string {

    return Object.keys(replacements).reduce((result, key) => {
        return result.replace(new RegExp(`{${key}}`, 'g'), replacements[key].toString());
    }, template);
}
// pick xml tag
export function pickXMLTag(xml: string, tag: string): string {
    const regex = new RegExp(`<${tag}>(.*?)</${tag}>`, 's');
    const match = xml.match(regex);
    if (match) {
        return match[1];
    }
    return '';
}

function maskEmail(email: string): string {
    // 对电子邮件地址进行部分隐藏处理
    const [localPart, domain] = email.split('@');
    const maskedLocalPart = localPart[0] + '*' + localPart.substring(1, localPart.length - 1).replace(/./g, '*') + localPart[localPart.length - 1];
    return maskedLocalPart + '@' + domain;
}

export function findAndMaskEmails(text: string): string {
    // 在给定的文本中查找电子邮件地址并对其进行部分隐藏处理
    const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    let match;
    while ((match = emailRegex.exec(text)) !== null) {
        const maskedEmail = maskEmail(match[0]);
        text = text.replace(match[0], maskedEmail);
    }
    return text;
}

/**
 * 根据导出类型获取 Excel 导出结构
 *
 * @param type 导出类型
 */
export function genExcelFormat(mapping: IHeaderMapping[]): string[] {

    const result: string[] = [];

    // eslint-disable-next-line @reolink/general/disable-for-each-method
    mapping.forEach((mapping, index) => {

        result.push(`--header`);

        result.push(`${getExcelIndex(index + 1)}:${mapping.key}:${mapping.value}`);
    });

    return result;
}

/**
 * 将字段序号转为 Excel 中的字段序号，如：a、..x、y、z、aa..
 *
 * @param num 字段的序号
 */
export function getExcelIndex(num: number): string {

    let index = '';

    while (num > 0) {

        // 转换为字母（a-z）
        index = String.fromCharCode((num - 1) % 26 + 97) + index;

        // 获取商，用于下次循环计算
        num = Math.floor((num - 1) / 26);
    }

    return index;
}
