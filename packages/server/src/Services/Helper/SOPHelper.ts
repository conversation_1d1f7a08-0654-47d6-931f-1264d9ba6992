import * as ORM from '@reolink-fx/orm';
import * as DAO from '#/DAO';
import * as Config from '@reolink-fx/config';
import * as Logs from '@reolink-fx/log';
import { ITicketFieldIds } from '../Decls/Chat';

export class SOPHelper {

    private readonly _sopDao = ORM.useRepository<DAO.Sops.IEntity>(DAO.Sops.Entity);

    private readonly _logs = Logs.useLogger('SOPHelper');

    private readonly _tfIdCfg = Config.useConfig<ITicketFieldIds>({
        path: 'ticketFieldIds',
    });

    private readonly _articleDao = ORM.useRepository<DAO.Articles.IEntity>(DAO.Articles.Entity);

    public async generateAllModelPrompts(): Promise<Record<string, string>> {

        const sops = await this._sopDao.find({
            where: {
                ticketFieldId: this._tfIdCfg.model
            }
        });

        if (sops.length === 0) {
            this._logs.error({
                'action': 'generateAllModelPrompts',
                'message': 'model sops not found',
                'data': {
                    ticketFieldId: this._tfIdCfg.model
                }
            });
            return {};
        }

        const group: Record<string, string[]> = {};

        for (const sop of sops) {

            const [modelCategory, model] = (() => {
                const t = sop.title.split(':');

                return [t.shift(), t.pop()];
            })();

            if (modelCategory && model) {
                if (!group[modelCategory]) {
                    group[modelCategory] = [];
                }
                group[modelCategory].push(model);
            }
        }

        const resp: Record<string, string> = {};

        for (const modelCategory of Object.keys(group)) {

            const models = group[modelCategory];

            const str = [
                `##### model type:${modelCategory} begin #####`,
                `###${modelCategory}###`,
                ...models.map(model => model),
                `###${modelCategory} end###`,
                `##### model type:${modelCategory} end #####`
            ].join('\n');

            resp[modelCategory] = str;
        }

        return resp;
    }

    public async generateTFPromptsById(tfId: string, placeholder: string): Promise<string> {

        const sops = await this._sopDao.find({
            where: {
                ticketFieldId: tfId
            }
        });

        if (sops.length === 0) {
            this._logs.error({
                'action': 'generateTFPromptsById',
                'message': 'model sops not found',
                'data': {
                    ticketFieldId: tfId
                }
            });
            return '';
        }

        const prompt = [`## Subcategory of ${placeholder} begin ##`, ...sops.map(sop => sop.title), `## Subcategory of ${placeholder} end ##`].join('\n');

        return prompt;

    }

    /**
     * 根据工单中 sop key，获取SOP内容，并从中解析出涉及的文章id和宏id
     * 将对应正文查出拼接
     *
     * @param sopKey sop key，账户内全局唯一
     */
    public async getRelateArticleAndMacros(sopKey: string): Promise<string> {

        //正则找到article
        //会生效的宏仅存在于task定义中，提及的无用

        //获取文章正文和宏内容，补充Prompt前后缀，拼接

        const sop = await this._sopDao.findOne({
            where: {
                key: sopKey,
            }
        });

        if (!sop) {
            return '';
        }

        const macros = await sop?.tasks.map((task: any) => task.macros).flat();

        const content = sop.content;

        const regex = /https:\/\/support\.reolink\.com\/hc\/[a-z]{2}-[a-z]{2}\/articles\/(\d+)/g;

        // 使用正则表达式的exec方法进行全局匹配
        let match;
        const articleIds = [];

        while ((match = regex.exec(content)) !== null) {
            // match[1] 包含当前匹配的文章ID
            articleIds.push(match[1]);
        }

        const articles = await this._articleDao.find({
            where: {
                id: ORM.$in(articleIds)
            }
        });

        return JSON.stringify({
            articles,
            macros
        });
    }

    public async getSOPByTitle(title: string): Promise<DAO.Sops.IEntity | null> {
        return this._sopDao.findOne({
            where: {
                title,
            }
        });
    }

    public async getSOPTitleByKey(key: string): Promise<string | null> {
        const sop = await this.getSOPByTitle(key);
        return sop?.title ?? null;
    }

}
