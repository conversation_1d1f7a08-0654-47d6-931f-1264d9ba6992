import * as ORM from '@reolink-fx/orm';
import * as DAO from '#/DAO';

export class ProductSpecHelper {

    private readonly _productSpecDao = ORM.useRepository(DAO.ProductSpecs.Entity);

    public async getProductContent(key: string): Promise<string> {
        const productSpec = await this._productSpecDao.findOne({
            where: {
                product: key
            }
        });
        return productSpec?.content ?? '';
    }

}
