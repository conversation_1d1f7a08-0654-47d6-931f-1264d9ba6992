import { OpenAI } from 'openai';
import { Stream } from 'openai/streaming';

export interface IBaseOpenAiClient {
    createEmbedding(input: string): Promise<OpenAI.CreateEmbeddingResponse>;
    createStreamChatCompletion(
        messages: OpenAI.ChatCompletionMessageParam[]
    ): Promise<Stream<OpenAI.ChatCompletionChunk>>;
    createChatCompletion(
        messages: OpenAI.ChatCompletionMessageParam[],
        needJsonResponse?: boolean
    ): Promise<OpenAI.ChatCompletion>;
}
