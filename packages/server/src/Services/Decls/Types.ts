export const DESENSITIZATION_FORMAT: IHeaderMapping[] = [
    { 'key': 'origin', 'value': '含有敏感信息的原文（必填）' },
    { 'key': 'result', 'value': 'AI 匹配' }
];
export const SOP_MATCH_FORMAT: IHeaderMapping[] = [
    { 'key': 'ticket', 'value': 'Ticket #' },
    { 'key': 'model', 'value': '型号' },
    { 'key': 'content', 'value': '首封邮件文本' },
    { 'key': 'correctSOP', 'value': '目标SOP' },
    { 'key': 'matchSOP', 'value': '匹配SOP' },
    { 'key': 'isMatch', 'value': '是否匹配' },
];

export const TICKET_FORMAT: IHeaderMapping[] = [
    { 'key': 'ticketId', 'value': '工单ID' },
    { 'key': 'airesp', 'value': 'AI 回复' },
    { 'key': 'concatenatedContent', 'value': '多轮对话' }
];

export interface IHeaderMapping {

    /**
     * 字段名
     */
    'key': string;

    /**
     * 导出 Excel 字段名
     */
    'value': string;
}
