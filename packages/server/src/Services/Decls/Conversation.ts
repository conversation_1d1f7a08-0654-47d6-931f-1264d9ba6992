import { IPushedStep } from '#/DAO/Conversations';

export interface ICreateConversationArgs {

    /**
     * zendesk对话id
     */
    zdConversationId?: string;

    /**
     * zendesk用户id
     */
    zdUserId?: string;

    /**
     * 是否为内部测试对话，1是，0否
     */
    isInternalTest?: number;

    /**
     * 对话角色
     */
    role?: EMessageRole;

    /**
     * 创建时间
     */
    createdAt: string;

    /**
     * 脱敏后的提问内容
     */
    desensitizedContent?: string;

    /**
     * 提问内容
     */
    content: string;

}

export interface IUpdateConversationByZdConversationIdArgs extends IConversationMetrics {
    zdConversationId: string;
    pushedStep?: IPushedStep;

}

export interface IConversationMetrics {
    intent?: string;
    category?: string;
    emotion?: number;
    sopId?: string;
    lastStepId?: string;
    pushedSteps?: IPushedStep[];
    isPassToAgent?: number;
    isCreateTicket?: number;
    issueResolved?: number;
}

export interface ICreateConversationResponse {

    /**
     * 对话id
     *
     * 取值范围：safe_uint
     */
    conversationId: string;
}

export interface IMarkConversationArgs {

    /**
     * 会话Id
     *
     * 取值范围: safe_uint
     */
    id: string;

    /**
     * 是否是错误意图（可标记）
     */
    isWrongIntent?: boolean;

    /**
     * 客服tag (方便BI分析使用)
     *
     */
    agentTags?: string[];

    /**
     * 客服评论
     *
     * 取值范围: 1 - 255 个字符
     */
    agentComments?: string;

}

export interface ICreateConversationFeedbackArgs {

    /**
     * 对话id
     *
     * 取值范围：safe_uint
     */
    conversationId: string;

    /**
     * 未解决问题的原因
     * too_much_irrelevant_information: 太多不相关信息   not_consistent_with_the_facts: 与事实不一致
     * provided_step_cannot_be_completed: 无法完成给出的步骤  waiting_time_is_too_long: 等待时间过长  other: 其他
     */
    problemUnresolvedReasons?: number[];

    /**
     * 问题是否得到解决
     *
     */
    issueResolved: boolean;

    /**
     * 额外评论
     *
     * 取值范围: 1 - 255 个字符
     */
    additionalComments?: string;

}

export interface IGetConversationListArgs {

    /**
     * 是否为内部测试
     */
    isInternalTest?: boolean;

    /**
     * 对话id
     */
    id?: string;

    /**
     * 开始时间
     *
     * 取值范围: safe_uint
     */
    startTime?: number;

    /**
     * 结束时间
     *
     * 取值范围: safe_uint
     */
    endTime?: number;

    /**
     * 问题分类 其它 咨询类 排查类
     */
    category?: number;

    /**
     * 最后识别的情绪
     */
    emotion?: number;

    /**
     * 问题是否得到解决
     *
     */
    issueResolved?: boolean;

    /**
     * 是否是错误意图（可标记）
     */
    isWrongIntent?: boolean;

    /**
     * 是否转人工
     */
    isPassToAgent?: boolean;

    /**
     * 是否创建工单
     */
    isCreateTicket?: boolean;

    /**
     * 最后应用的sopid
     */
    sopId?: string;

    /**
     * 返回多少条记录
     * 可选范围为 1 - 1000
     */
    limit: number;

    /**
     * 分页页数
     * 正整数从 1 开始
     */
    page: number;
}

export interface IViewAdminConversation {

    /**
     * 会话Id
     *
     * 取值范围: safe_uint
     */
    id: number;

    /**
     * 用户Id，未登录为0
     *
     * 取值范围: safe_uint
     */
    userId: number;

    /**
     * 最后识别的意图
     */
    intent: string;

    /**
     * 问题分类 其它 咨询类 排查类
     */
    category: number;

    /**
     * 最后识别的情绪
     */
    emotion: number;

    /**
     * 是否是错误意图（可标记）
     */
    isWrongIntent: boolean;

    /**
     * 客服评论
     *
     * 取值范围: 1 - 255 个字符
     */
    agentComments: string;

    /**
     * 是否转人工
     */
    isPassToAgent: boolean;

    /**
     * 是否创建工单
     */
    isCreateTicket: boolean;

    /**
     * 最后应用的sopid
     */
    sopId: number;

    /**
     * 最后推送的步骤id
     */
    lastStepId: number;

    /**
     * 已推送的SOP步骤
     */
    pushedSteps: Array<{

        sopId: string;

        /**
         * 意图
         */
        intent: string;

        steps: Array<{

            /**
             * 步骤id
             */
            stepId: string;

            /**
             * 步骤
             */
            step: string;

            /**
             * 是否推送提示
             */
            isPushTip: boolean;
        }>;
    }>;

    /**
     * 问题是否得到解决
     *
     */
    issueResolved: boolean;

    /**
     * 未解决问题的原因
     * too_much_irrelevant_information: 太多不相关信息   not_consistent_with_the_facts: 与事实不一致
     * provided_step_cannot_be_completed: 无法完成给出的步骤  waiting_time_is_too_long: 等待时间过长  other: 其他
     */
    problemUnresolvedReason?: 'too_much_irrelevant_information' | 'not_consistent_with_the_facts'
    | 'provided_step_cannot_be_completed' | 'waiting_time_is_too_long' | 'other';

    /**
     * 额外评论
     *
     * 取值范围: 1 - 255 个字符
     */
    additionalComments: string;

    /**
     * 反馈时间
     *
     * 取值范围: safe_uint
     */
    feedbackCreatedAt: number;

    /**
     * 会话信息
     *
     * 取值范围: 1 - 65535 个字符
     */
    messages: Array<{

        /**
             * 消息发送者
             *
             * 取值范围: user: 用户, assistant: 助手
             */
        role: 'user' | 'assistant';

        /**
             * 内容
             *
             */
        content: string;

        /**
             * 创建时间
             *
             * 取值范围: safe_uint
             */
        createdAt: number;

    }>;

    /**
     * 创建时间
     *
     * 取值范围: safe_uint
     */
    createdAt: number;
}

export interface ICreateConversationMessageArgs {

    messageId: string;
    conversationId: string;
    role: EMessageRole;
    message: string;
}

export interface ICreateConversationMessageByZdConversationIdArgs {

    zdConversationId: string;
    role: EMessageRole;
    desensitizedMessage: string;
    message: string;
    createdAt: string;
}
export enum EMessageRole {
    ASSISTANT,
    USER
}

export enum EProblemUnresolvedReason {
    TOO_MUCH_IRRELEVANT_INFORMATION = 1,
    NOT_CONSISTENT_WITH_THE_FACTS,
    PROVIDED_STEP_CANNOT_BE_COMPLETED,
    WAITING_TIME_IS_TOO_LONG,
    PAGE_INTERACTION,
    OTHER
}

export enum EConversationCategory {
    OTHER = 0,
    CONSULTATION = 1,
    TROUBLESHOOTING = 2
}

export enum EConversationEmotion {
    NEUTRAL = 0,
    POSITIVE = 1,
    NEGATIVE = 2,
    EXTREMELY_NEGATIVE = 3
}
