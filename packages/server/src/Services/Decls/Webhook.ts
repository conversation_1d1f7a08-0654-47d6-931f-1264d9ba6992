export interface IWebhookMessage {
    app: {
        id: string;
    };
    webhook: {
        id: string;
        version: string;
    };
    events: Array<{
        id: string;
        type: string;
        createdAt: string;
        payload: {
            conversation: {
                id: string;
                type: string;
            };
            message: {
                id: string;
                received: string;
                content: {
                    type: string;
                    text: string;
                };
                author: {
                    type: string;
                    userId: string;
                    displayName: string;
                    user: {
                        id: string;
                    };
                };
                source: {
                    type: string;
                    integrationId: string;
                };
            };
        };
    }>;
}
