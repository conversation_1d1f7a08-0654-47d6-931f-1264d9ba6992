import { IViewSolution } from './Sop';

export interface IChatResponse {

    /**
     * 消息对象。
     */
    message: IMessage;

    /**
     * 对话的唯一标识符。
     */
    conversationId: string;

    /**
     * 错误信息symbol。
     */
    error: string;
}

export interface IReplyPromptArgs {
    category: string;
    inquiry: string;
    summary: string;
    model: string;
    reference: string;
    context: string;
}
export interface IChatWithSceneResponse {

    /**
     * 消息id
     */
    messageId: string;

    /**
     * 对话的唯一标识符。
     */
    conversationId: string;

    /**
     * ai的回复
     */
    aiContent: string;
}

export interface IChatArgs {
    scene: string;
    content: string;
    conversationId: string;
    params?: Record<string, unknown>;
}

/**
   * 表示响应中的单个消息。
   */
export interface IMessage {

    /**
     * 消息的唯一标识符。
     */
    id: string;

    /**
     * 消息创建的时间戳，单位为毫秒的 Unix 时间戳。
     */
    createdAt: number;

    /**
     * 消息的内容。
     */
    content: IMessageContent;

    // /**
    //  * 消息的状态，例如 "in_progress"。
    //  */
    // status: 'in_progress' | 'finished';

}

/**
   * 表示消息内容的结构。
   */
export interface IMessageContent {

    /**
     * 内容的类型
     */
    contentType: 'text';

    /**
     * 组成内容的部分数组。
     */
    parts: string[];
}

export enum ESSEChannel {

    AI_CHAT = 'ai-chat',

    AI_CHAT_RECALL = 'ai-chat-recall'
}

export enum EChatScene {
    CHAT = 'chat',
    CHAT_SELF_INSPECTION_SCENE = 'ai-recall',
    CLASSIFICATION = 'classification',
    TICKET_SUMMARIZATION = 'ticket-summary',
    SEQUENT_REPLY = 'sequent-reply',
}

export const CHAT_SELF_INSPECTION_SCENE = 'ai-recall';

export const CHAT_SCENE = 'reolink-zd';

export const CHAT_SEARCH_SCENE = 'ai-search';

export interface ISelfInspectionMessage {

    response: {
        reason: string[];
        result: string[];
    };
}

export const CHAT_END_SYMBOL = '[DONE]';

export const NOT_FOUND_SLUG_RESPONSE = 'Oops, The information you provided did not return any results. Please try again with other information.';

export interface ITicketFieldIds {
    'productInquiry': string;
    'troubleshooting': string;
    'orderBusiness': string;
    'feedbackSuggestion': string;
    'complaints': string;
    'informationSecurity': string;
    model: string;
}

export interface ISOP {
    id: number; // SOP ID
    description: string; // SOP 名称
    intent: string; // ⽤户意图； ⽤户意图全集 中的⼀项
    solutions: IViewSolution;
}
