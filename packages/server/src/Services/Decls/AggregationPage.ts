export enum EAggregationPageItemTypeInt {
    PRODUCT_INFO = 1,
    SUPPORT_ARTICLE = 2,
    FIRMWARE = 3,
    USERMANUAL = 4,
    NOCONTENT = 5
}

export enum EAggregationPageStatusInt {
    DRAFT = 1,
    PUBLISHED = 2
}

export interface IAggregationPage {

    /**
     * 聚合页key
     */
    'slug': string;

    /**
     * 聚合页id
     *
     */
    'id': string;

    /**
     * 聚合页标题
     *
     */
    'title': string;

    /**
     * 聚合页描述
     *
     */
    'describe': string;

    /**
     * 聚合页item
     */
    'items': IAggregationPageItem[];

}

/**
 * 聚合页item
 * 目前只需要article 和 product类型的
 */
export interface IAggregationPageItem {

    /**
     * 聚合页item id
     *
     */
    'id': string;

    /**
     * 聚合页item标题
     *
     */
    'title': string;

    /**
     * 聚合页item 内容
     *
     */
    'content': string;
}

export interface IArticleVector {
    id: string;
    idx: number;
    vector: number[];
}
