import { Annotation } from '@langchain/langgraph';
import OpenAI from 'openai';
import { ROUTER_DECISION, EEmotion } from '../agents/troubleshooting/constants';
// import { EEmotion } from '../agents/troubleshooting/constants';

export interface IStepInfo {
    /**
     * 步骤ID
     */
    stepId?: string;
    /**
     * 步骤描述
     */
    step?: string;
    /**
     * 选项
     */
    actions?: IAction[];
    /**
     * 是否完成
     */
    done?: boolean;

    /**
     * 用户选项的文本
     */
    text?: string;

    /**
     * 用户选择的选项
     */
    selectOption?: string;
}

/**
 * chat 方法参数接口
 */
export interface IChatParams {
    /**
     * 用户发送的消息
     */
    userMessage: string;
    /**
     * 当前会话的唯一标识符
     */
    conversationId: string;
    /**
     * 应用的唯一标识符
     */
    appId: string;
    /**
     * 指示这是否是一个新的会话
     */
    isNewConversation: boolean;
    /**
     * 用户在步骤中做出的选择或条件，可选
     */
    conditions?: IStepInfo;

    /**
     * 输入类型 控制处理方式
     */
    inputType?: 'text' | 'reply';
}

// This defines the object that is passed between each node
export const agentState = Annotation.Root({
    routingDecision: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    currentNode: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    emotion: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    appId: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    conversationId: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    productModel: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    productCategory: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    category: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    messages: Annotation<OpenAI.ChatCompletionMessageParam[]>({
        reducer: (x, y) => x.concat(y),
        default: () => []
    }),
    intent: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    response: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    status: Annotation<string>({
        reducer: (x: string | undefined, y: string | undefined): string => y ?? x ?? '',
        default: () => '',
    }),
    doneSteps: Annotation<string[]>({
        reducer: (x: string[] | undefined, y: string[] | undefined): string[] => {
            const currentSteps = x ?? [];
            const newSteps = y ?? [];
            return [...new Set([...currentSteps, ...newSteps])];
        },
        default: () => []
    }),
    conditions: Annotation<IStepInfo>({
        reducer: (x: IStepInfo | undefined, y: IStepInfo | undefined): IStepInfo => y ?? x ?? {},
        default: () => ({})
    }),
    chooseCondition: Annotation<IStepInfo>({
        reducer: (x: IStepInfo | undefined, y: IStepInfo | undefined): IStepInfo => y ?? x ?? {},
        default: () => ({})
    }),
    inputType: Annotation<'text' | 'reply'>({
        reducer: (x: 'text' | 'reply' | undefined, y: 'text' | 'reply' | undefined): 'text' | 'reply' => y ?? x ?? 'text',
        default: () => 'text',
    })
});

export function createBaseState(state: typeof agentState.State): typeof agentState.State {
    return {
        ...state,
        messages: []
    };
}

export function firstParallelStepRouter(state: Record<string, unknown>): string {
    if (state.emotion === EEmotion.EXTREMELY_NEGATIVE) {
        return ROUTER_DECISION.AGENT;
    }
    if (state.productCategory === 'null') {
        return ROUTER_DECISION.PICK;
    }
    if (state.category === 'consultation') {
        return ROUTER_DECISION.CONSULT;
    }
    return ROUTER_DECISION.CONTINUE;
}

export function consultRouter(state: Record<string, unknown>): string {
    if (state.response !== '') {
        return ROUTER_DECISION.END;
    }
    return ROUTER_DECISION.CONTINUE;
}

// 用于跟踪意图识别失败次数的内存映射
const intentRecognitionFailures = new Map<string, number>();

export function intentRouter(state: Record<string, unknown>): string {
    const conversationId = state.conversationId as string;
    if (state.intent === '' && state.response !== '') {
        // 意图识别失败，增加计数
        if (conversationId) {
            const currentFailCount = intentRecognitionFailures.get(conversationId) ?? 0;
            const newFailCount = currentFailCount + 1;
            intentRecognitionFailures.set(conversationId, newFailCount);
            // 如果失败次数达到3次，则转人工
            if (newFailCount >= 3) {
                // 重置计数（可选）
                intentRecognitionFailures.delete(conversationId);
                return ROUTER_DECISION.AGENT;
            }
        }
        return ROUTER_DECISION.END;
    }
    if (state.intent !== '') {
        // 意图识别成功，重置计数
        if (conversationId) {
            intentRecognitionFailures.set(conversationId, 0);
        }
        return ROUTER_DECISION.CONTINUE;
    }
    return ROUTER_DECISION.END;
}

export const PRESET_TAGS = [
    'Home Hub',
    'Battery WIFI',
    'Battery 4G',
    'DC WIFI',
    'NVR',
    'PoE',
    'DC 4G',
    'Accessories'
];

// Define IAction interface (Ensure this definition is present and correct)
export interface IAction {
    type: string;
    text: string;
    payload?: string;
    metadata?: Record<string, any>;
}
