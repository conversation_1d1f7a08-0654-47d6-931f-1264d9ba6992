import * as DAO from '#/DAO';

export interface ISolution {
    steps: Array<{
        stepId: string;
        priority: number;
    }>;
}

export interface IViewStep {
    stepId: string;
    step: string;
    options: IOptions;
    priority: number;
}

export interface IViewSolution {
    steps: IViewStep[];
}

export interface IViewSop extends DAO.SopsV2.Entity {
    scene: string;
    solutions: IViewSolution;
}

export interface ICreateSopArgs {
    sceneId: string;
    intent: string;
    description: string;
    solutions: ISolution;
    tags: string[];
}

export interface IUpdateSopArgs {

    sopId: string;
    intent?: string;
    description?: string;
    sceneId?: string;
    solutions?: ISolution;
    tags?: string[];
}

export interface IGetSopListArgs {

    /**
     * id列表
     */
    ids?: string[];

    /**
     * 意图
     *
     * 取值范围: 1-255 个字符
     */
    intent?: string;

    /**
     * 产品分类
     */
    tag?: string;

    /**
     * 是否返回不包含产品分类
     */
    noTags?: boolean;

    /**
     * 返回多少条记录
     * 可选范围为 1-1000
     */
    limit?: number;

    /**
     * 分页页数
     * 正整数从1开始
     */
    page?: number;
}

export interface ICreateStepArgs {
    step: string;
    options: IOptions;
}

export interface IOptions {
    articleId?: string;
    description?: string;
}

export interface IUpdateStepArgs {
    step?: string;
    options?: IOptions;
}

export interface IGetStepListArgs {

    /**
     * 步骤名称
     *
     * 取值范围: 1-512 个字符
     */
    step?: string;

    /**
     * 返回多少条记录
     * 可选范围为 1-1000
     */
    limit: number;

    /**
     * 分页页数
     * 正整数从1开始
     */
    page: number;
}

export interface ICreateSceneArgs {
    scene: string;
}

export interface IGetSceneListArgs {

    /**
     * 场景名称
     *
     * 取值范围: 1-512 个字符
     */
    scene?: string;

    /**
     * 返回多少条记录
     * 可选范围为 1-1000
     */
    limit: number;

    /**
     * 分页页数
     * 正整数从1开始
     */
    page: number;
}

export interface IUpdateSceneArgs {
    sceneId: string;
    scene: string;
}
