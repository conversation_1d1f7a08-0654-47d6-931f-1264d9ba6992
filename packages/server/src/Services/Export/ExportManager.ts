import * as DI from '@reolink-fx/di';
import * as Config from '@reolink-fx/config';
import * as RPC from '@reolink-fx/rpc';
import * as $HClient from '@litert/http-client';
import * as ExportRecords from '@reolink-services/export-records';
import * as _ from '@reolink-fx/utils';
import * as Logs from '@reolink-fx/log';
import * as Metric from '@reolink-fx/metrics';
import * as $C from '#/Services/Export/Export';
import { SERVICE_NAME } from '@reolink-services/ai-support';

export const EXPORT_MAXIMUM_ITEM = 2000;

export const EXPORT_SCENE = 'aisupport_data_export';

interface IExportList {
    'taskId': string;
    'scene': string;
    'zipName': string;
    'dirPath': string;
    'file': string;
    'index': string[];
    'headerDefinition': Record<string, string>;
}

@DI.Singleton()
export class ExportManager {

    @Config.BindConfig({
        'path': 'export',
        'validation': {
            'path': 'string(1, 64)',
            'maximum': 'uint(1, 200000)'
        }
    })
    private readonly _exportCfg!: $C.IExportCfg;

    private readonly _taskList: Record<string, IExportList> = {};

    @Logs.UseLogger('ExportManager')
    private readonly _logs!: Logs.ILogger;

    private readonly _ooc = DI.use(Metric.OutOfControlReporter);

    private readonly _rpc = RPC.useClient<ExportRecords.IInternalAPIs>({
        'service': 'ExportRecords'
    });

    private readonly _hcli = $HClient.createHttpClient();

    public createTask<T>(
        merchant: number,
        args: T,
        createdBy: number
    ): Promise<ExportRecords.TaskAPIs.ICreateExportTaskResponse> {

        const task: ExportRecords.TaskAPIs.ICreateExportTaskArgs = {
            'service': SERVICE_NAME,
            'merchant': merchant,
            'scene': EXPORT_SCENE,
            'args': args,
            'createdBy': createdBy
        };

        return this._rpc.apis.createExportTask(task, {});
    }

    public async createUploadSession(
        args: ExportRecords.TaskAPIs.ICreateUploadSessionArgs
    ): Promise<ExportRecords.TaskAPIs.ICreateUploadSessionResponse> {

        return this._rpc.invoke('CreateUploadSession', args);
    }

    public async upload(url: string, file: string): Promise<void> {

        this._logs.info({
            'action': 'upload_file',
            'message': `start upload file`,
            'data': { url }
        });

        await this._hcli.request({
            'method': 'PUT',
            'url': url,
            'version': 1.1,
            'headers': {
                'Content-Type': 'application/octet-stream',
                'Connection': 'keep-alive',
                'content-length': await _.File.getFileSize(file)
            },
            'data': _.File.createReadStream(file)
        });

        this._logs.info({
            'action': 'upload_file',
            'message': `upload file success`
        });
    }

    public getHeadMap(records: Record<string, string>): $C.IHeadMap[] {

        const rows: $C.IHeadMap[] = [];

        for (const key in records) {
            rows.push({
                key: key,
                title: records[key]
            });
        }

        return rows;
    }

    public async updateProgressRate(taskId: string, current: number, message: string = '正在导出'): Promise<void> {

        await this._rpc.invoke('UpdateProgress', {
            id: taskId,
            progress: {
                status: 'processing',
                current: current,
                message: message
            },
        });
    }

    public async completed(
        taskId: string,
        headerMap: $C.IHeadMap[],
        message: string = '导出成功',
        indexFile: string = 'index.json'
    ): Promise<void> {

        await this._rpc.invoke('UpdateProgress', {
            id: taskId,
            progress: {
                status: 'success',
                message: message,
                result: {
                    status: 'success',
                    message: message
                }
            },
            excelOptions: {
                indexFile: indexFile,
                headerMap: headerMap
            },
        });
    }

    public async failed(
        taskId: string,
        error: Record<string, unknown>,
        message: string = '导出失败'
    ): Promise<void> {
        await this._rpc.invoke('UpdateProgress', {
            id: taskId,
            progress: {
                status: 'error',
                message: message,
                result: error
            }
        });
    }

    public async startExport(
        scene: string,
        taskId: string,
        headerDefinition: Record<string, string>
    ): Promise<void> {
        const dir = `${this._exportCfg.path}/${taskId}`;

        const zipName = `${taskId}.tar.gz`;
        const file = `${this._exportCfg.path}/${zipName}`;
        await this.mkDir(dir);
        this._taskList[taskId] = {
            'dirPath': dir,
            'file': file,
            'scene': scene,
            'zipName': zipName,
            'taskId': taskId,
            'index': [],
            'headerDefinition': headerDefinition
        };
    }

    public async recycle(taskId: string): Promise<void> {
        if (!this._taskList[taskId]) {
            return;
        }

        await this.del(this._taskList[taskId].dirPath, this._taskList[taskId].file);
        delete this._taskList[taskId];
    }

    public getHeader(
        fullHeader: Record<string, string>,
        fields?: string[]
    ): Record<string, string> {

        if (!fields?.length) {

            return fullHeader;
        }

        const filteredHeader: Record<string, string> = {};

        for (const field of fields) {
            if (Object.prototype.hasOwnProperty.call(fullHeader, field)) {
                filteredHeader[field] = fullHeader[field];
            }
        }

        return filteredHeader;
    }

    public async process<T>(
        taskId: string,
        data: T[],
        headerDefinition: Record<string, string>
    ): Promise<void> {

        if (data.length === 0) {
            await this.failed(taskId, { 'error': $C.EXPORT_DATA_NOT_FOUND }, $C.EXPORT_DATA_NOT_FOUND);
            return;
        }

        const dir = `${this._exportCfg.path}/${taskId}`;
        const zipName = `${taskId}.tar.gz`;
        const file = `${this._exportCfg.path}/${zipName}`;

        try {

            await this.mkDir(dir);

            const num = Math.ceil(data.length / this._exportCfg.maximum);

            const sliceSize = this._exportCfg.maximum;

            const fileIndexes: string[] = [];

            for (let i = 0; i < num; i++) {

                const index = i + 1;

                const filename = this.getFilename(taskId, index);

                const filePath = this.getFilePath(dir, filename);

                const records = data.splice(0, sliceSize);

                await _.File.writeFile(
                    filePath,
                    this.rowsToString<T>(EXPORT_SCENE, taskId, records)
                );

                fileIndexes.push(filename);
            }

            const indexPath = this.getFilePath(dir, $C.INDEX_FILENAME);

            await _.File.writeFile(indexPath, JSON.stringify(fileIndexes));

            await this.package(taskId, zipName);

            const session = await this.createUploadSession({
                filename: this.getExportFileName(EXPORT_SCENE),
                id: taskId
            });

            await this.upload(session.url, file);

            await this.completed(
                taskId,
                this.getHeadMap(headerDefinition)
            );

        }
        catch (e: any) {

            this._ooc.report({
                'module': 'export',
                'problem': 'export_error',
                'context': {
                    taskId: taskId,
                    error: _.Errors.errorToJson(e)
                }
            });

            await this.failed(taskId, e, e.message ?? $C.UNKNOWN_ERROR);

        }
        finally {
            await this.del(dir, file);
        }
    }

    public async mkDir(dir: string): Promise<void> {

        await _.File.mkdir(dir);
    }

    public getExportFileName(scene: string): string {

        const rand = _.String.randomString(6, _.String.RAND_CHARSET_09);

        const date = _.DateTime.parseLocalTime();

        return `${scene}-${date.yr}${date.mo + 1}${date.day}${date.hr}${date.min}-${rand}.xlsx`;
    }

    /**
     * 返回文件名
     * @returns string
     */
    public getFilename(taskId: string, index: number = 1): string {

        return `${taskId}_${index}.json`;
    }

    public getFilePath(dir: string, filename: string): string {
        return `${dir}/${filename}`;
    }

    /**
     * 返回多行的字符串
     * @returns string
     */
    public rowsToString<T>(scene: string, taskId: string, data: T[]): string {

        for (const record of data) {

            for (const key in record) {

                if (
                    typeof record[key] !== 'number' &&
                    typeof record[key] !== 'boolean' &&
                    !_.Validation.isString(record[key])
                ) {

                    record[key] = '' as any;

                    this._logs.error({
                        'action': 'rowsToString',
                        'message': 'export_invalid_value',
                        'data': {
                            type: scene,
                            taskId: taskId,
                            key: key,
                            value: record[key],
                            record: record
                        }
                    });
                }

                if (typeof record[key] === 'number') {

                    record[key] = ((record[key] as unknown as number).toString()) as unknown as T[typeof key];
                }

                if (typeof record[key] === 'boolean') {

                    record[key] = (record[key] as unknown as boolean ? 'T' : 'F') as unknown as T[typeof key];
                }

            }

        }

        return JSON.stringify(data);
    }

    public async del(dir: string, zip: string): Promise<void> {

        const [isDirExist, isZipExist] = await Promise.all([
            _.File.pathExists(dir),
            _.File.pathExists(zip)
        ]);

        if (isDirExist) {
            await _.File.removeFiles([dir], {
                'useSystemCommand': true,
                'force': true,
                'recursive': true
            });
        }

        if (isZipExist) {
            await _.File.removeFiles([zip]);
        }
    }

    public async package(taskId: string, zipName: string): Promise<void> {

        await _.File.execAt('tar', ['-cf', zipName, '-C', taskId, '.'], this._exportCfg.path);
    }
}
