import { LLMService } from './LLMService';
import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { agentState } from '../../Decls/TroubleShootingFlow';
import { ConversationHandler } from '../conversation/ConversationHandler';
import { PromptManager } from '#/Services/PromptManager';
import { AggregationPageManager } from '#/Services/AggregationPageManager';

/**
 * 产品分类检测器
 * 负责检测用户描述的产品类别
 */
@DI.Singleton()
export class ConsultProblemSolver {
    private readonly _llmService = DI.use(LLMService);

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _logger = LOG.useLogger('EmotionDetector');

    private readonly _conversationHandler = DI.use(ConversationHandler);

    private readonly _aggregationPageMgr = DI.use(AggregationPageManager);

    /**
     * 检测产品分类
     * @param state 当前状态
     * @returns 更新后的状态
     */
    public async getAggregationPageResponse(state: typeof agentState.State): Promise<string> {
        try {
            this._logger.info({
                action: 'GetAggregationPageResponse',
                message: '获取聚合页问题回复',
                data: { messages: state.messages }
            });

            const lastUserMessage = this._llmService.getLastUserMessage(state.messages);

            const slugs = await this._aggregationPageMgr.detectSlug(
                lastUserMessage,
                state.productModel
            );

            if (!slugs.length) {
                return '';
            }

            const systemPrompt = (await this._promptManager.generateAggregationPrompt(lastUserMessage, {
                'slugs': slugs,
                'productModel': state.productModel
            }))?.prompt ?? '';

            const response = await this._llmService.createChatCompletion(systemPrompt, state.messages);

            await this._conversationHandler.saveConversationMetricsToCache(
                {
                    zdConversationId: state.conversationId
                });

            return response;
        }
        catch (error) {
            this._logger.error({
                action: 'getAggregationPageResponse',
                message: '获取聚合页问题回复失败',
                data: { error }
            });
            return '';
        }
    }
}
