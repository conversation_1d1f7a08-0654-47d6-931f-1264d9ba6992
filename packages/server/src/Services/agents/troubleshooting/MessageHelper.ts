import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { SunshineConversationClient } from '#/Services/Clients/SunshineConversationClient';
import { ConversationHandler } from '../conversation/ConversationHandler';
import { PromptManager } from '#/Services/PromptManager';
import { LLMService } from './LLMService';

/**
 * 消息帮助类
 * 提供消息翻译和发送的通用方法
 */
@DI.Singleton()
export class MessageHelper {
    private readonly _logger = LOG.useLogger('MessageHelper');

    private readonly _conversationHandler = DI.use(ConversationHandler);

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _llmService = DI.use(LLMService);

    private readonly _sunshineConversationClient = DI.use(SunshineConversationClient);

    /**
     * 翻译消息并发送给客户
     * @param appId 应用ID
     * @param conversationId 对话ID
     * @param messageText 要翻译的消息文本
     * @returns Promise<void>
     */
    public async sendTranslatedMessage(appId: string, conversationId: string, messageText: string): Promise<void> {
        try {
            // 获取最后一条用户消息
            const lastUserMessage = await this._conversationHandler.getLastUserMessageFromCache(conversationId);

            // 翻译文本
            const translatedMessage = await this._promptManager.generateTranslateTextToUserLanguagePrompt(
                lastUserMessage ?? '',
                messageText
            );

            const translatedContent = await this._llmService.createChatCompletion(translatedMessage, []);

            // 发送消息
            const message = {
                type: 'text' as const,
                markdownText: translatedContent
            };
            await this._sunshineConversationClient.sendMessage(appId, conversationId, message);

            this._logger.info({
                action: 'sendTranslatedMessage',
                message: '成功发送翻译后的消息给客户',
                data: { appId, conversationId }
            });
        }
        catch (error) {
            this._logger.error({
                action: 'sendTranslatedMessage',
                message: '发送翻译后的消息失败',
                data: { error, appId, conversationId }
            });
        }
    }
}
