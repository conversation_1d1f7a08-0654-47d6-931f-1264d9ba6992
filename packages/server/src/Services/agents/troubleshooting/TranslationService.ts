import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { LLMService } from './LLMService';
import { PromptManager } from '#/Services/PromptManager';

/**
 * 翻译服务类
 * 提供多语言文本翻译功能
 */
@DI.Singleton()
export class TranslationService {
    private readonly _logger = LOG.useLogger('TranslationService');

    private readonly _llmService = DI.use(LLMService);

    private readonly _promptManager = DI.use(PromptManager);

    /**
     * 翻译多段文本为用户使用的语言
     * @param userMessage 用户消息，用于检测用户语言
     * @param texts 需要翻译的文本数组
     * @returns 翻译后的文本数组，顺序与输入一致
     */
    public async translateTexts(
        userMessage: string,
        texts: string[]
    ): Promise<string[]> {
        try {
            // 如果没有需要翻译的文本，直接返回空数组
            if (!texts || texts.length === 0) {
                return [];
            }

            // 将多段文本用换行符连接成一个字符串
            const combinedText = texts.join('\n');

            // 生成翻译提示词
            const prompt = await this._promptManager.generateTranslateTextToUserLanguagePrompt(
                userMessage,
                combinedText
            );

            // 调用LLM服务进行翻译
            const response = await this._llmService.createChatCompletion(prompt, []);

            // 将翻译结果按换行符分割回数组
            const translatedTexts = response.split('\n');

            // 确保返回的数组长度与输入一致
            if (translatedTexts.length !== texts.length) {
                this._logger.info({
                    action: 'translateTexts',
                    message: '翻译结果数量与原文本数量不一致',
                    data: {
                        originalCount: texts.length,
                        translatedCount: translatedTexts.length
                    }
                });

                // 如果翻译结果数量不一致，尝试匹配尽可能多的文本
                return translatedTexts.slice(0, texts.length);
            }

            return translatedTexts;
        }
        catch (error) {
            this._logger.error({
                action: 'translateTexts',
                message: '文本翻译失败',
                data: {
                    error
                }
            });
            // 翻译失败时返回原文本
            return texts;
        }
    }
}
