/**
 * 故障排除工作流常量定义
 */

// 步骤选项常量
export const STEP_OPTIONS = ['yes', 'not sure'];
export const ONE_STEP_OPTIONS = ['yes'];

// 状态常量
export const STATUS = {
    NO_MATCH: 'no_match',
    PARTIAL_MATCH: 'partial_match',
    COMPLETE_MATCH: 'complete_match'
};

// 路由决策常量
export const ROUTER_DECISION = {
    CONTINUE: 'continue',
    CATEGORY: 'category',
    END: 'end',
    INTENT: 'intent',
    PICK: 'pick',
    PROCESS_STEP: 'processStep',
    AGENT: 'agent',
    COMFORT: 'comfort',
    CONSULT: 'consult',
    RESOLVED: 'resolved'
};

export enum EEmotion {
    NEUTRAL = '0',
    POSITIVE = '1',
    NEGATIVE = '2',
    EXTREMELY_NEGATIVE = '3'
}

export enum EPromptScene {
    DESENSITIZE_MESSAGE = 'desensitization_message',
    ANSWER_SOP = 'answer_sop',
    DETECT_CATEGORY = 'detect_category',
    DETECT_QUESTION_CATEGORY = 'detect_question_category',
    DETECT_EMOTION = 'detect_emotion',
    CHOOSE_INTENT = 'choose_intent',
    TRANSLATE_STEP = 'translate_step',
    DETECT_CUSTOMER_STEP_STATUS = 'detect_customer_step_status',
    COMFORT_CUSTOMER = 'comfort_customer',
    TRANSLATE_TEXT_OR_DESCRIPTION = 'translate_text_or_description',
    TRANSLATE_TEXT_TO_USER_LANGUAGE = 'translate_text_to_user_language',
    DISPATCHER_AGENT = 'dispatcher_agent',
    GUIDE_AGENT = 'guide_agent',
    RESOLVED_AGENT = 'resolved_agent'
}

// 节点名称常量
export const NODE_NAMES = {
    START: '__start__',
    END: '__end__',
    CATEGORY_DETECTOR: 'CategoryDetector',
    QUESTION_CATEGORY_DETECTOR: 'QuestionCategoryDetector',
    CONSULT_PROBLEM_SOLVER: 'ConsultProblemSolver',
    INTENT_SEARCH: 'IntentSearchAgent',
    PROCESS_STEP: 'ProcessStep',
    ANSWER_AGENT: 'AnswerAgent',
    EMOTION_DETECTOR: 'EmotionDetector',
    USER_INPUT: 'UserInput',
    PASS_TO_AGENT: 'PassToAgent',
    FIRST_PARALLEL_STEP: 'FirstParallelStep',
    DISPATCHER: 'DispatcherAgent',
    GUIDE_AGENT: 'GuideAgent',
    RESOLVED_AGENT: 'ResolvedAgent'
};

// 预设标签
export const PRESET_TAGS = [
    'Home Hub',
    'Battery WIFI',
    'Battery 4G',
    'DC WIFI',
    'NVR',
    'PoE',
    'DC 4G',
    'Accessories'
];
