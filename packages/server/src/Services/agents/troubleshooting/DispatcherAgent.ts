import { LLMService } from './LLMService';
import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { agentState } from '../../Decls/TroubleShootingFlow';
import {  EPromptScene, ROUTER_DECISION } from './constants';
import { PromptManager } from '#/Services/PromptManager';

/**
 * 调度Agent
 * 负责分析用户消息和状态，将用户路由到合适的专门Agent处理
 */
@DI.Singleton()
export class DispatcherAgent {
    private readonly _llmService = DI.use(LLMService);

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _logger = LOG.useLogger('DispatcherAgent');

    /**
     * 分析用户消息并确定路由方向
     * @param state 当前状态
     * @returns 路由决策
     */
    public async dispatch(state: typeof agentState.State): Promise<string> {
        try {
            this._logger.info({
                action: 'dispatch',
                message: '开始分析用户消息并进行路由',
                data: {
                    intent: state.intent,
                    emotion: state.emotion,
                    category: state.category,
                    conditions: state.conditions
                }
            });

            // 获取调度Agent的prompt
            const systemPrompt = await this._promptManager.generatePromptByScene(EPromptScene.DISPATCHER_AGENT, '');

            // 构建prompt上下文
            let promptWithContext = systemPrompt
                .replace('{$CURRENT_INTENT}', state.intent ?? '')
                .replace('{$USER_MESSAGE}', this._llmService.getLastUserMessage(state.messages))
                .replace('{$CURRENT_STEP}', state.conditions?.step ?? '');

            // 简化的对话历史
            promptWithContext = promptWithContext.replace('{$CONVERSATION_HISTORY}', '[]');

            // 调用LLM获取路由决策
            const dispatchResult = await this._llmService.createChatCompletion(promptWithContext, state.messages);

            // 解析路由决策
            const routingDecision = this._parseRoutingDecision(dispatchResult);

            this._logger.info({
                action: 'dispatch',
                message: '路由决策结果',
                data: {
                    routingDecision,
                    rawResult: dispatchResult
                }
            });

            // 根据解析结果返回对应的路由决策常量
            return this._mapToRouterDecision(routingDecision);
        }
        catch (error) {
            this._logger.error({
                action: 'dispatch',
                message: '调度分析失败',
                data: { error }
            });
            // 默认路由到意图识别
            return ROUTER_DECISION.INTENT;
        }
    }

    /**
     * 解析LLM返回的路由决策
     * @param result LLM返回的结果
     * @returns 路由决策agent名称
     */
    private _parseRoutingDecision(result: string): string {
        try {
            // 尝试从结果中提取路由决策部分
            const routingRegex = /<Routing Decision>(.*?)<\/Routing Decision>/s;
            const routingExec = routingRegex.exec(result);
            if (routingExec?.[1]) {
                return routingExec[1].trim();
            }

            this._logger.info({
                action: '_parseRoutingDecision',
                message: '无法从结果中提取路由决策，将使用默认值',
                data: { result }
            });

            return 'intent_matcher'; // 默认路由到意图匹配
        }
        catch (e) {
            this._logger.error({
                action: '_parseRoutingDecision',
                message: '解析路由决策失败',
                data: { error: e, result }
            });
            return 'intent_matcher'; // 默认路由到意图匹配
        }
    }

    /**
     * 将Agent名称映射到路由决策常量
     * @param agentName Agent名称
     * @returns 路由决策常量
     */
    private _mapToRouterDecision(agentName: string): string {
        const mappings: Record<string, string> = {
            // guide_agent用于处理与chatbot能力无关的问题，结束当前流程
            'guide_agent': ROUTER_DECISION.END,
            'intent_matcher': ROUTER_DECISION.INTENT,
            'consult_problem_solver': ROUTER_DECISION.CONSULT,
            'step_processor': ROUTER_DECISION.PROCESS_STEP,
            'human_agent': ROUTER_DECISION.AGENT,
            'resolved_agent': ROUTER_DECISION.RESOLVED
        };

        return mappings[agentName] || ROUTER_DECISION.INTENT;
    }
}
