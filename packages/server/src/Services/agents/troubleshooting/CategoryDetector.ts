import { LLMService } from './LLMService';
import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { agentState, createBaseState } from '../../Decls/TroubleShootingFlow';
import { EPromptScene, PRESET_TAGS } from './constants';
import { PromptManager } from '#/Services/PromptManager';
import { ConversationHandler } from '../conversation/ConversationHandler';

/**
 * 产品分类检测器
 * 负责检测用户描述的产品类别
 */
@DI.Singleton()
export class CategoryDetector {
    private readonly _llmService = DI.use(LLMService);

    private readonly _logger = LOG.useLogger('CategoryDetector');

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _conversationHandler = DI.use(ConversationHandler);

    /**
     * 检测产品分类
     * @param state 当前状态
     * @returns 更新后的状态
     */
    public async detect(state: typeof agentState.State): Promise<typeof agentState.State> {

        const productModel = await this._conversationHandler.getProductModelFromCache(state.conversationId);

        try {
            this._logger.info({
                action: 'detect',
                message: '开始产品分类检测',
                data: { messages: state.messages }
            });

            const systemPrompt = await this._promptManager.generatePromptByScene(EPromptScene.DETECT_CATEGORY, productModel ?? '');

            const content = await this._llmService.createChatCompletion(systemPrompt, []);

            this._logger.info({
                action: 'detect',
                message: '产品分类检测结果',
                data: { productCategory: content }
            });

            // 如果无法确定分类，提供预设选项让用户选择
            if (content === 'null') {
                return await this._buildProductCategorySelectOptions(state);
            }

            return {
                ...createBaseState(state),
                productModel: productModel ?? '',
                productCategory: content
            };
        }
        catch (error) {
            this._logger.error({
                action: 'detect',
                message: '产品分类检测失败',
                data: { error }
            });
            return {
                ...createBaseState(state),
                productCategory: 'null',
                productModel: productModel ?? '',
                conditions: {
                    step: '设备分类',
                    actions: PRESET_TAGS.map(tag => ({
                        type: 'reply',
                        text: tag,
                        payload: tag,
                        metadata: {
                            stepId: 'category'
                        }
                    }))
                }
            };
        }
    }

    /**
     * 构建产品分类选择选项
     * @param state 当前状态
     * @returns 更新后的状态
     */
    private async _buildProductCategorySelectOptions(state: typeof agentState.State): Promise<typeof agentState.State> {

        const systemPrompt = await this._promptManager.generateTranslateTextToUserLanguagePrompt(
            this._llmService.getLastUserMessage(state.messages),
            'Please select your Reolink product category.'
        );
        const content = await this._llmService.createChatCompletion(
            systemPrompt,
            []
        );

        return {
            ...createBaseState(state),
            productCategory: 'null',
            conditions: {
                stepId: 'category',
                step: content,
                actions: PRESET_TAGS.map(tag => ({
                    type: 'reply',
                    text: tag,
                    payload: tag,
                    metadata: {
                        stepId: 'category'
                    }
                }))
            }
        };
    }
}
