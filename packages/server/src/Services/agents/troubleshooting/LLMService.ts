import { OpenAI } from 'openai';
import * as DI from '@reolink-fx/di';
import * as _ from '@reolink-fx/utils';
import * as LOG from '@reolink-fx/log';
import { OpenAIClientFactory } from '#/Services/Clients/OpenAIClientFactory';
import { IBaseOpenAiClient } from '#/Services/Decls/BaseOpenAiClient';

/**
 * 语言模型服务类
 * 处理所有与LLM相关的调用，提供统一接口
 */
@DI.Singleton()
export class LLMService {

    private readonly _openAIFactory = DI.use(OpenAIClientFactory);

    private _openAIClient!: IBaseOpenAiClient;

    private readonly _logger = LOG.useLogger('LLMService');

    @DI.Initializer()
    protected _init(): void {
        this._openAIClient = this._openAIFactory.getClient();
    }

    /**
   * 创建聊天完成
   * @param systemPrompt 系统提示
   * @param messages 用户消息
   * @returns LLM响应内容
   */
    public async createChatCompletion(
        systemPrompt: string,
        messages: OpenAI.ChatCompletionMessageParam[],
        needJsonResponse: boolean = false
    ): Promise<string> {
        try {
            const allMessages: OpenAI.ChatCompletionMessageParam[] = [
                { role: 'system', content: systemPrompt },
                ...messages
            ];

            console.log('systemPrompt', systemPrompt);
            console.log('messages', messages);

            const startTime = Date.now();
            const response = await this._openAIClient.createChatCompletion(allMessages, needJsonResponse);
            const endTime = Date.now();

            const content = response.choices[0].message.content ?? '';

            console.log('ai response', content);

            this._logger.info({
                action: 'createChatCompletion',
                message: 'LLM调用完成',
                data: {
                    duration: endTime - startTime
                }
            });

            return content;
        }
        catch (error) {
            this._logger.error({
                action: 'createChatCompletion',
                message: 'LLM调用失败',
                data: {
                    error
                }
            });
            throw new Error(`LLM调用失败: ${JSON.stringify(error)}`);
        }
    }

    public getLastUserMessage(messages: OpenAI.ChatCompletionMessageParam[]): string {
        const userMessages = messages.filter(message => message.role === 'user');
        return userMessages[userMessages.length - 1].content as string;
    }

    /**
   * 解析JSON响应
   * @param content LLM返回的内容
   * @param defaultValue 默认值
   * @returns 解析后的JSON对象
   */
    public parseJSON<T>(content: string, defaultValue: T): T {
        return _.String.parseJSON<T>(content, {
            onError: (error) => {
                this._logger.error({
                    action: 'parseJSON',
                    message: 'JSON解析失败',
                    data: {
                        error,
                        content
                    }
                });
                return defaultValue;
            }
        });
    }

}
