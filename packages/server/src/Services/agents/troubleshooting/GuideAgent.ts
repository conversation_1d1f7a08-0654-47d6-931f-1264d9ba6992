import { LLMService } from './LLMService';
import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { agentState } from '../../Decls/TroubleShootingFlow';
import { EPromptScene } from './constants';
import { PromptManager } from '#/Services/PromptManager';

/**
 * 产品分类检测器
 * 负责检测用户描述的产品类别
 */
@DI.Singleton()
export class GuideAgent {
    private readonly _llmService = DI.use(LLMService);

    private readonly _logger = LOG.useLogger('GuideAgent');

    private readonly _promptManager = DI.use(PromptManager);

    /**
     * 检测产品分类
     * @param state 当前状态
     * @returns 更新后的状态
     */
    public async guideUser(state: typeof agentState.State): Promise<string> {
        try {
            this._logger.info({
                action: 'GuideAgent',
                message: '开始引导用户',
                data: { messages: state.messages }
            });

            const systemPrompt = await this._promptManager.generatePromptByScene(
                EPromptScene.GUIDE_AGENT,
                this._llmService.getLastUserMessage(state.messages)
            );

            const content = await this._llmService.createChatCompletion(systemPrompt, state.messages);

            this._logger.info({
                action: 'GuideAgent',
                message: '引导用户结果',
                data: { response: content }
            });

            return content;
        }
        catch (error) {
            this._logger.error({
                action: 'GuideAgent',
                message: '引导用户失败',
                data: { error }
            });
            return '';
        }
    }
}
