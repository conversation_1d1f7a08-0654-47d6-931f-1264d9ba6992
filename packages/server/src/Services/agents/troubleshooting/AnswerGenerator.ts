import { LLMService } from './LLMService';
import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { agentState, createBaseState } from '../../Decls/TroubleShootingFlow';
import { PromptManager } from '#/Services/PromptManager';
import { EPromptScene } from './constants';

/**
 * 答案生成器
 * 负责生成最终用户回答
 */
@DI.Singleton()
export class AnswerGenerator {
    private readonly _llmService = DI.use(LLMService);

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _logger = LOG.useLogger('AnswerGenerator');

    /**
   * 生成回答
   * @param state 当前状态
   * @returns 更新后的状态
   */
    public async generateAnswer(state: typeof agentState.State): Promise<typeof agentState.State> {
        try {
            this._logger.info({
                action: 'generateAnswer',
                message: '开始生成回答',
                data: {
                    intent: state.intent,
                    status: state.status,
                    response: state.response
                }
            });

            const systemPrompt = await this._promptManager.generatePromptByScene(
                EPromptScene.ANSWER_SOP,
                state.response
            );
            const answer = await this._llmService.createChatCompletion(systemPrompt, state.messages);

            this._logger.info({
                action: 'generateAnswer',
                message: '回答生成完成',
                data: { answer }
            });

            return {
                ...createBaseState(state),
                response: answer,
                conditions: {},
                messages: [{ role: 'assistant', content: answer }]
            };
        }
        catch (error) {
            this._logger.error({
                action: 'generateAnswer',
                message: '生成回答失败',
                data: { error }
            });
            return {
                ...createBaseState(state),
                response: '抱歉，生成回答时出现了问题。请稍后再试。',
                messages: [{ role: 'assistant', content: '抱歉，生成回答时出现了问题。请稍后再试。' }]
            };
        }
    }
}
