import { LLMService } from './LLMService';
import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { SopManager } from '../../sop/SopManager';
import { agentState, createBaseState } from '../../Decls/TroubleShootingFlow';
import { IViewSop } from '../../Decls/Sop';
import { NODE_NAMES, STATUS } from './constants';
import { ChatCompletionMessageParam } from 'openai/resources/chat/completions';
import { ConversationHandler } from '../conversation/ConversationHandler';
import { PromptManager } from '#/Services/PromptManager';
import { MessageHelper } from './MessageHelper';

// 首次无法匹配到SOP时的文案
const firstNoMatchText = `Thanks for reaching out! To better assist you, could you please provide more details about the issue? For example, let us know if the camera fails to power on, can't connect to WiFi, keeps going offline. Also, let us know when the issue occurs and any troubleshooting steps you've already tried. This will help us understand the situation and offer a more accurate solution.`;

// 交互第一次无法匹配到SOP时的文案
const secondNoMatchText = `Thank you again for getting back to us! We're still having some difficulty pinpointing the exact issue with the details provided so far. To help us assist you more effectively, could you please share more specific information about the situation? For example, when the issue occurs, whether the device or setup worked fine previously, or if any recent changes were made to the device or setup. The more context you provide, the faster we can help resolve it!`;

// 交互第二次无法匹配到SOP时的文案
const thirdNoMatchText = `Thank you for providing the detailed information. We've received your description, but this issue requires further assistance. One of our support agents will follow up with you via email shortly. Please kindly wait for their response—we appreciate your patience!`;

/**
 * 意图匹配器
 * 负责匹配用户意图并查找相关SOP
 */
@DI.Singleton()
export class IntentMatcher {
    private readonly _llmService = DI.use(LLMService);

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _sopManager = DI.use(SopManager);

    private readonly _conversationHandler = DI.use(ConversationHandler);

    private readonly _logger = LOG.useLogger('IntentMatcher');

    private readonly _messageHelper = DI.use(MessageHelper);

    // 存储会话匹配尝试次数的内存缓存
    private readonly _noMatchCountMap: Map<string, number> = new Map();

    /**
   * 搜索和匹配用户意图
   * @param state 当前状态
   * @returns 更新后的状态
   */
    public async matchIntent(state: typeof agentState.State): Promise<typeof agentState.State> {
        try {
            this._logger.info({
                action: 'matchIntent',
                message: '开始意图匹配',
                data: { productCategory: state.productCategory, messages: state.messages }
            });

            // 获取相关SOP列表
            const sops = await this._getRelevantSops(state.productCategory);

            if (sops.length === 0) {
                return await this._handleNoSopsFound(state);
            }

            // 构建SOP意图字符串
            const intentsText = this._buildIntentsText(sops);

            // 获取产品型号
            const productModel = await this._conversationHandler.getProductModelFromCache(state.conversationId);

            // 调用LLM匹配意图
            const result = await this._findMatchingIntent(
                intentsText,
                state.productCategory,
                productModel ?? '',
                state.messages
            );

            // 验证匹配的意图是否存在  TODO 匹配到的意图可能会有多个
            const isValidIntent = await this._validateIntent(result.intent);

            if (!isValidIntent) {
                return await this._handleNoIntentMatch(state);
            }
            else {
                //发送提示文案给客户 按照步骤走
                await this.sendIntentMatchedMessage(
                    state.appId,
                    state.conversationId
                );
            }

            if (state.conversationId) {
                // 记录意图匹配结果 TO conversation
                await this._conversationHandler.saveConversationMetricsToCache(
                    {
                        zdConversationId: state.conversationId,
                        intent: result.intent
                    });
            }

            return {
                ...createBaseState(state),
                currentNode: NODE_NAMES.INTENT_SEARCH,
                conditions: {},
                intent: result.intent,
                messages: result.intent !== '' ? [{ role: 'assistant', content: result.intent }] : [],
                response: result.response ?? ''
            };
        }
        catch (error) {
            this._logger.error({
                action: 'matchIntent',
                message: '意图匹配失败',
                data: { error }
            });
            return this._handleError(state);
        }
    }

    /**
     * 向用户发送意图匹配成功的提示消息
     * @param appId 应用ID
     * @param conversationId 对话ID
     * @returns Promise<void>
     */
    public async sendIntentMatchedMessage(
        appId: string,
        conversationId: string
    ): Promise<void> {
        const messageText = "Thank you for the detailed description! Based on the information you've provided, we've identified some steps that may help resolve the issue. Please follow the instructions below to continue with the troubleshooting. Your cooperation is greatly appreciated!";
        await this._messageHelper.sendTranslatedMessage(appId, conversationId, messageText);
    }

    /**
     * 获取指定会话的意图匹配尝试次数
     * @param conversationId 对话ID
     * @returns number 匹配尝试次数
     */
    private _getNoMatchCount(conversationId: string): number {
        return this._noMatchCountMap.get(conversationId) ?? 0;
    }

    /**
     * 增加指定会话的意图匹配尝试次数
     * @param conversationId 对话ID
     */
    private _incrementNoMatchCount(conversationId: string): void {
        const currentCount = this._getNoMatchCount(conversationId);
        this._noMatchCountMap.set(conversationId, currentCount + 1);
    }

    /**
     * 根据匹配尝试次数获取相应的文案
     * @param conversationId 对话ID
     * @returns string 对应的文案
     */
    private _getNoMatchTextByCount(conversationId: string): string {
        const count = this._getNoMatchCount(conversationId);
        if (count >= 3) {
            return thirdNoMatchText;
        }
        else if (count === 2) {
            return secondNoMatchText;
        }
        else {
            return firstNoMatchText;
        }
    }

    /**
   * 获取相关的SOP列表
   * @param productCategory 产品分类
   * @returns SOP列表
   */
    private async _getRelevantSops(productCategory: string): Promise<IViewSop[]> {
        try {
            if (productCategory !== 'null') {
                return (await this._sopManager.getSopList({ 'tag': productCategory, noTags: true }))[0];
            }
            return (await this._sopManager.getSopList({}))[0];
        }
        catch (error) {
            this._logger.error({
                action: '_getRelevantSops',
                message: '获取SOP列表失败',
                data: { error, productCategory }
            });
            return [];
        }
    }

    /**
   * 构建意图文本字符串
   * @param sops SOP列表
   * @returns 意图文本
   */
    private _buildIntentsText(sops: IViewSop[]): string {
        return sops.map((sop, index) =>
      `
### intent ${index + 1} ###

intent: ${sop.intent}

scene: ${sop.scene}

description: ${sop.description}
######
`).join('\n');
    }

    /**
   * 查找匹配的意图
   * @param intentsText 意图文本
   * @param messages 用户消息
   * @returns 匹配结果
   */
    private async _findMatchingIntent(
        intentsText: string,
        productCategory: string,
        productModel: string,
        messages: ChatCompletionMessageParam[]
    ): Promise<{ intent: string; response: string; }> {
        const systemPrompt = await this._promptManager.generateChooseIntentPrompt(
            intentsText,
            productCategory,
            productModel
        );
        const content = await this._llmService.createChatCompletion(systemPrompt, messages, true);

        this._logger.info({
            action: '_findMatchingIntent',
            message: '意图匹配结果',
            data: { result: content }
        });

        return this._llmService.parseJSON<{ intent: string; response: string; }>(
            content,
            { intent: '', response: '' }
        );
    }

    /**
   * 验证意图是否存在
   * @param intent 意图
   * @returns 是否存在
   */
    private async _validateIntent(intent: string): Promise<boolean> {
        if (!intent) return false;

        const sop = (await this._sopManager.getSopList({ 'intent': intent }))[0];
        return sop.length > 0;
    }

    /**
   * 处理未找到SOP的情况
   * @param state 当前状态
   * @returns 更新后的状态
   */
    private async _handleNoSopsFound(state: typeof agentState.State): Promise<typeof agentState.State> {
        this._incrementNoMatchCount(state.conversationId);
        const noMatchText = this._getNoMatchTextByCount(state.conversationId);
        const prompt = await this._promptManager.generateTranslateTextToUserLanguagePrompt(
            this._llmService.getLastUserMessage(state.messages),
            noMatchText
        );
        const response = await this._llmService.createChatCompletion(prompt, []);
        return {
            ...createBaseState(state),
            intent: '',
            conditions: {},
            response,
            status: STATUS.NO_MATCH
        };
    }

    /**
   * 处理未找到匹配意图的情况
   * @param state 当前状态
   * @returns 更新后的状态
   */
    private async _handleNoIntentMatch(state: typeof agentState.State): Promise<typeof agentState.State> {
        this._incrementNoMatchCount(state.conversationId);
        const noMatchText = this._getNoMatchTextByCount(state.conversationId);
        const prompt = await this._promptManager.generateTranslateTextToUserLanguagePrompt(
            this._llmService.getLastUserMessage(state.messages),
            noMatchText
        );
        const response = await this._llmService.createChatCompletion(prompt, []);
        // 如果是第三次无法匹配，需要转人工客服
        const noMatchCount = this._getNoMatchCount(state.conversationId);
        if (noMatchCount >= 2) {
            this._logger.info({
                action: '_handleNoIntentMatch',
                message: '第三次无法匹配意图，准备转人工客服',
                data: { conversationId: state.conversationId }
            });
        }
        return {
            ...createBaseState(state),
            intent: '',
            conditions: {},
            response,
            currentNode: NODE_NAMES.INTENT_SEARCH,
            status: STATUS.NO_MATCH
        };
    }

    /**
   * 处理错误情况
   * @param state 当前状态
   * @returns 更新后的状态
   */
    private _handleError(state: typeof agentState.State): typeof agentState.State {
        const response = 'Sorry, there was an error processing your request. Please try again later.';

        return {
            ...createBaseState(state),
            intent: '',
            conditions: {},
            currentNode: NODE_NAMES.INTENT_SEARCH,
            response,
            status: STATUS.NO_MATCH
        };
    }
}
