import { LLMService } from './LLMService';
import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { agentState } from '../../Decls/TroubleShootingFlow';
import { EPromptScene } from './constants';
import { ConversationHandler } from '../conversation/ConversationHandler';
import { PromptManager } from '#/Services/PromptManager';

/**
 * 产品分类检测器
 * 负责检测用户描述的产品类别
 */
@DI.Singleton()
export class QuestionCategoryDetector {
    private readonly _llmService = DI.use(LLMService);

    private readonly _logger = LOG.useLogger('QuestionCategoryDetector');

    private readonly _conversationHandler = DI.use(ConversationHandler);

    private readonly _promptManager = DI.use(PromptManager);

    /**
     * 检测产品分类
     * @param state 当前状态
     * @returns 更新后的状态
     */
    public async detectQuestionCategory(state: typeof agentState.State): Promise<string> {
        try {
            this._logger.info({
                action: 'detectQuestionCategory',
                message: '开始问题分类检测',
                data: { messages: state.messages }
            });

            const systemPrompt = await this._promptManager.generatePromptByScene(EPromptScene.DETECT_QUESTION_CATEGORY, '');
            const content = await this._llmService.createChatCompletion(systemPrompt, state.messages);

            this._logger.info({
                action: 'detectQuestionCategory',
                message: '问题分类检测结果',
                data: { category: content }
            });

            await this._conversationHandler.saveConversationMetricsToCache(
                {
                    zdConversationId: state.conversationId,
                    category: content
                });

            return content;
        }
        catch (error) {
            this._logger.error({
                action: 'detectQuestionCategory',
                message: '问题分类检测失败',
                data: { error }
            });
            return 'null';
        }
    }
}
