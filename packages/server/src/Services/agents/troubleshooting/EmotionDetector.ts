import { LLMService } from './LLMService';
import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { agentState } from '../../Decls/TroubleShootingFlow';
import { EEmotion, EPromptScene } from './constants';
import { ConversationHandler } from '../conversation/ConversationHandler';
import { SunshineConversationClient } from '#/Services/Clients/SunshineConversationClient';
import { PromptManager } from '#/Services/PromptManager';

/**
 * 产品分类检测器
 * 负责检测用户描述的产品类别
 */
@DI.Singleton()
export class EmotionDetector {
    private readonly _llmService = DI.use(LLMService);

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _logger = LOG.useLogger('EmotionDetector');

    private readonly _conversationHandler = DI.use(ConversationHandler);

    private readonly _sunshineConversationClient = DI.use(SunshineConversationClient);

    /**
     * 检测产品分类
     * @param state 当前状态
     * @returns 更新后的状态
     */
    public async detectEmotion(state: typeof agentState.State): Promise<string> {
        try {
            this._logger.info({
                action: 'detectEmotion',
                message: '开始情绪检测',
                data: { messages: state.messages }
            });

            const systemPrompt = await this._promptManager.generatePromptByScene(EPromptScene.DETECT_EMOTION, '');
            const emotion = await this._llmService.createChatCompletion(systemPrompt, state.messages);

            this._logger.info({
                action: 'detectEmotion',
                message: '情绪检测结果',
                data: { emotion: emotion }
            });

            await this._conversationHandler.saveConversationMetricsToCache(
                {
                    zdConversationId: state.conversationId,
                    emotion: Number(emotion)
                });

            //如果情绪过于消极，则需要发送一个安慰的信息
            if (emotion === EEmotion.EXTREMELY_NEGATIVE || emotion === EEmotion.NEGATIVE) {
                const comfortPrompt = await this._promptManager.generatePromptByScene(EPromptScene.COMFORT_CUSTOMER, '');
                const comfort = await this._llmService.createChatCompletion(comfortPrompt, state.messages);
                await this._sunshineConversationClient.sendMessage(state.appId, state.conversationId, {
                    type: 'text',
                    markdownText: comfort
                });
            }

            return emotion;
        }
        catch (error) {
            this._logger.error({
                action: 'detectEmotion',
                message: '情绪检测失败',
                data: { error }
            });
            return EEmotion.NEUTRAL;
        }
    }
}
