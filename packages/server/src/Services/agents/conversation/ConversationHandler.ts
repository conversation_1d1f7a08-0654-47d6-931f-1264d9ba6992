import * as Cache from '@reolink-fx/redis';
import * as DI from '@reolink-fx/di';
import { ConversationManager } from '#/Services/ConversationManager';
import * as LOG from '@reolink-fx/log';
import * as _ from '@reolink-fx/utils';
import * as Config from '@reolink-fx/config';
import { EMessageRole, IConversationMetrics, IUpdateConversationByZdConversationIdArgs } from '#/Services/Decls/Conversation';
import { IPushedStep } from '#/DAO/Conversations';
import { LLMService } from '../troubleshooting/LLMService';
import { PromptManager } from '#/Services/PromptManager';
import { EPromptScene } from '../troubleshooting/constants';
import { SopManager } from '#/Services/sop/SopManager';

export const CONVERSATION_CACHE_KEY = 'conversation:messages';
export const CONVERSATION_METRICS_KEY = 'conversation:metrics';

export const INTERNAL_TEST_CONVERSATION_CACHE_PREFIX = 'conversation:test';

export interface IConversationMessage {
    zdConversationId: string;
    zdUserId: string;
    messageId: string;
    role: EMessageRole;
    content: string;
    createdAt: string;
}

export interface IUserInfo {
    name: string;
    email: string;
}

@DI.Singleton()
export class ConversationHandler {

    private readonly _cache = Cache.useRedisClient();

    private readonly _logger = LOG.useLogger('ConversationHandler');

    private readonly _llmService = DI.use(LLMService);

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _conversationManager = DI.use(ConversationManager);

    private readonly _sopManager = DI.use(SopManager);

    private readonly _config = Config.useConfig<{
        saveInterval: number;
    }>({
        'path': 'conversationHandlerOptions'
    });

    @DI.Initializer()
    protected _init(): void {
        _.Async.invokeAsync(async () => {
            while (true) {
                try {
                    await this._saveMessagesToDB();
                    await this._saveConversationMetricsToDB();
                }
                catch (error) {
                    this._logger.error({
                        action: 'saveMessageToDB',
                        message: '保存会话消息到数据库失败',
                        data: { error: JSON.stringify(error) }
                    });
                }
                await _.Async.sleep(this._config.saveInterval * 1000);
            }
        });
    }

    private async _calculateCompletionPercentage(pushedStep: IPushedStep): Promise<number> {
        try {
            const sop = await this._sopManager.getSopById(pushedStep.sopId);
            if (!sop) {
                return 0;
            }

            const totalSteps = sop.solutions.steps.length;
            if (totalSteps === 0) {
                return 0;
            }

            const completedSteps = pushedStep.steps.length;
            return Number((completedSteps / totalSteps).toFixed(2));
        }
        catch (error) {
            this._logger.error({
                action: '_calculateCompletionPercentage',
                message: '计算完成百分比失败',
                data: { error, pushedStep }
            });
            return 0;
        }
    }

    private _mergePushedSteps(
        currentPushedSteps: readonly IPushedStep[] | undefined | null,
        pushedStepsToMergeInput: IPushedStep | readonly IPushedStep[] | undefined | null
    ): IPushedStep[] {
        // Deep clone currentPushedSteps to avoid modifying the original, or start with an empty array
        const finalPushedSteps: IPushedStep[] = currentPushedSteps
            ? _.String.parseJSON(JSON.stringify(currentPushedSteps), {
                onError: (error, json) => {
                    this._logger.error({
                        action: 'parsePushedSteps',
                        message: '解析推送步骤失败',
                        data: { error, json }
                    });
                    return [];
                }
            })
            : [];

        if (!pushedStepsToMergeInput) {
            return finalPushedSteps;
        }

        // Normalize pushedStepsToMergeInput to be an array
        const pushedStepsToMergeArray: readonly IPushedStep[] = Array.isArray(pushedStepsToMergeInput)
            ? pushedStepsToMergeInput
            : [pushedStepsToMergeInput];

        if (pushedStepsToMergeArray.length === 0) {
            return finalPushedSteps;
        }

        for (const sopToMerge of pushedStepsToMergeArray) {
            if (!sopToMerge) { // Handle cases where an array might contain null/undefined entries
                continue;
            }
            // Clone the item being merged to avoid side-effects
            const newSop = _.String.parseJSON<IPushedStep>(JSON.stringify(sopToMerge), {
                onError: (error, json) => {
                    this._logger.error({
                        action: 'parsePushedSteps',
                        message: '解析推送步骤失败',
                        data: { error, json }
                    });
                    return {
                        sopId: '',
                        intent: '',
                        steps: []
                    };
                }
            });

            const existingSop = finalPushedSteps.find(sop => sop.sopId === newSop.sopId);

            if (existingSop) {
                if (newSop.steps) { // Ensure the new SOP has steps to merge
                    for (const newStep of newSop.steps) {
                        const stepIndex = existingSop.steps.findIndex(step => step.stepId === newStep.stepId);
                        if (stepIndex !== -1) {
                            existingSop.steps[stepIndex] = newStep; // Update existing step
                        }
                        else {
                            existingSop.steps.push(newStep); // Add new step
                        }
                    }
                }
            }
            else {
                // SOP does not exist in finalPushedSteps, add the cloned new SOP
                finalPushedSteps.push(newSop);
            }
        }

        return finalPushedSteps;
    }

    public async savePassControlConversation(conversationId: string): Promise<void> {
        await this._cache.set(`passControl:${conversationId}`, 'true');
    }

    public async checkPassControlConversation(conversationId: string): Promise<boolean> {
        const passControlInCache = await this._cache.get(`passControl:${conversationId}`);
        return passControlInCache !== null;
    }

    /**
     * 缓存已经处理的事件 10分钟
     * @param eventId 事件id
     */
    public async saveProcessedEvent(eventId: string): Promise<void> {
        await this._cache.setEX(eventId, 'true', 60 * 10 * 1000);
    }

    public async checkProcessedEvent(eventId: string): Promise<boolean> {
        const eventInCache = await this._cache.get(eventId);
        return eventInCache !== null;
    }

    public async checkConversationIfExist(zdConversationId: string): Promise<boolean> {
        // 先检查缓存
        const conversationInCache = await this._cache.get(zdConversationId);
        if (conversationInCache) {
            return true;
        }
        // 缓存中不存在则检查数据库
        return this._conversationManager.checkConversationIfExist(zdConversationId);
    }

    public async saveMessageToCache(message: IConversationMessage): Promise<void> {
        // 保存conversation cache
        await this._cache.setEX(message.zdConversationId, '-', 60 * 30 * 1000);
        await this._cache.hSet(CONVERSATION_CACHE_KEY, message.messageId, JSON.stringify(message));

        if (message.role === EMessageRole.USER) {
            await this._cache.setEX(`lastUserMessage:${message.zdConversationId}`, message.content, 24 * 60 * 60 * 1000); // 1 day expiry
        }
    }

    public async saveFirstMessageToCache(conversationId: string, messageId: string): Promise<void> {
        await this._cache.setEX(`firstMessage:${conversationId}`, messageId, 60 * 10 * 1000 * 60);
    }

    public async getFirstMessageFromCache(conversationId: string): Promise<string | null> {
        return this._cache.get(`firstMessage:${conversationId}`);
    }

    private async _desensitizeMessage(message: string): Promise<string> {
        const prompt = await this._promptManager.generatePromptByScene(EPromptScene.DESENSITIZE_MESSAGE, message);
        const desensitizedMessage = await this._llmService.createChatCompletion(prompt, []);
        return desensitizedMessage;
    }

    private async _saveMessagesToDB(): Promise<void> {

        const messages = await this._cache.hGetAll(CONVERSATION_CACHE_KEY);

        for (const messageId in messages) {
            const messageStr = messages[messageId];
            if (messageStr) {
                try {
                    await this._saveMessage(messageStr, messageId);
                }
                catch (error) {
                    this._logger.error({
                        action: 'saveMessagesToDB',
                        message: '保存会话消息到数据库失败',
                        data: {
                            messageId,
                            error
                        }
                    });
                }
            }
        }

    }

    private async _saveMessage(messageStr: string, messageId: string): Promise<void> {

        const message = _.String.parseJSON<IConversationMessage>(messageStr, {
            onError: (error, json) => {
                this._logger.error({
                    action: 'parseMessage',
                    message: '解析会话消息失败',
                    data: { error, json }
                });
                return {
                    zdConversationId: '',
                    zdUserId: '',
                    messageId: '',
                    role: EMessageRole.ASSISTANT,
                    content: json,
                    createdAt: ''
                };
            }
        });
        //检查conversation是否存在
        const conversation = await this._conversationManager.checkConversationIfExist(message.zdConversationId);
        const isInternalTest = await this._checkIfInternalTest(message.zdConversationId);

        // 对消息进行脱敏 只存储脱敏后的消息
        const desensitizedMessage = await this._desensitizeMessage(message.content);

        if (!conversation) {
            await this._conversationManager.createConversation({
                zdConversationId: message.zdConversationId,
                zdUserId: message.zdUserId,
                desensitizedContent: desensitizedMessage,
                role: message.role,
                content: message.content,
                createdAt: message.createdAt,
                isInternalTest: isInternalTest
            },
            {});
        }
        else {
            await this._conversationManager.createConversationMessageByZdConversationId({
                zdConversationId: message.zdConversationId,
                role: message.role,
                desensitizedMessage: desensitizedMessage,
                message: message.content,
                createdAt: message.createdAt
            });
        }

        await this._cache.hDel(CONVERSATION_CACHE_KEY, messageId);
    }

    private async _saveConversationMetricsToDB(): Promise<void> {
        const metrics = await this._cache.hGetAll(CONVERSATION_METRICS_KEY);
        for (const zdConversationId in metrics) {
            const metricsStr = metrics[zdConversationId];
            if (metricsStr) {
                await this._saveConversationMetrics(metricsStr, zdConversationId);
            }
        }
    }

    private async _saveConversationMetrics(metricsStr: string, zdConversationId: string): Promise<void> {
        try {
            const currMetrics = _.String.parseJSON<IConversationMetrics>(metricsStr, {
                onError: (error, json) => {
                    this._logger.error({
                        action: 'parseConversationMetrics',
                        message: '解析会话指标失败',
                        data: { error, json },
                    });
                    return {};
                },
            });

            const updateData: IUpdateConversationByZdConversationIdArgs = {
                zdConversationId: zdConversationId,
                ...currMetrics
            };

            // 如果缓存中没有 pushedSteps，直接更新其他字段，保留数据库中的 pushedSteps
            if (currMetrics.pushedSteps === undefined) {
                delete updateData.pushedSteps;
                await this._conversationManager.updateConversationByZdConversationId(updateData);
                await this._cache.hDel(CONVERSATION_METRICS_KEY, zdConversationId);
                return;
            }

            // 只有在需要更新 pushedSteps 时才执行以下操作
            const existingConversation = await this._conversationManager.getConversationByZdConversationId(
                zdConversationId
            );

            const finalPushedSteps = this._mergePushedSteps(
                existingConversation?.pushedSteps,
                currMetrics.pushedSteps
            );

            // 计算每个 SOP 的完成百分比
            for (const pushedStep of finalPushedSteps) {
                pushedStep.completionPercentage = await this._calculateCompletionPercentage(pushedStep);
            }

            updateData.pushedSteps = finalPushedSteps;

            await this._conversationManager.updateConversationByZdConversationId(updateData);
            await this._cache.hDel(CONVERSATION_METRICS_KEY, zdConversationId);
        }
        catch (error) {
            this._logger.error({
                action: '_saveConversationMetrics',
                message: '保存会话指标到数据库失败',
                data: {
                    zdConversationId,
                    error
                }
            });
        }
    }

    public async getConversationPushedStep(conversationId: string): Promise<IPushedStep[]> {
        const metricsStr = await this._cache.hGet(CONVERSATION_METRICS_KEY, conversationId);
        if (metricsStr) {
            const currMetrics = _.String.parseJSON<IConversationMetrics>(metricsStr, {
                onError: (error, json) => {
                    this._logger.error({
                        action: 'parseConversationMetrics',
                        message: '解析会话指标失败',
                        data: { error, json }
                    });
                    return {};
                }
            });
            return currMetrics.pushedSteps ?? [];
        }
        else {
            const conversation = await this._conversationManager.getConversationByZdConversationId(conversationId);
            if (conversation) {
                return conversation.pushedSteps;
            }
        }
        return [];
    }

    public async saveConversationMetricsToCache(
        args: IUpdateConversationByZdConversationIdArgs
    ): Promise<void> {
        const metricsStr = await this._cache.hGet(CONVERSATION_METRICS_KEY, args.zdConversationId);
        if (metricsStr) {
            const currMetrics = _.String.parseJSON<IConversationMetrics>(metricsStr, {
                onError: (error, json) => {
                    this._logger.error({
                        action: 'parseConversationMetrics',
                        message: '解析会话指标失败',
                        data: { error, json },
                    });
                    return {};
                },
            });

            let mergedPushedSteps = currMetrics.pushedSteps;
            if (args.pushedStep !== undefined) {
                mergedPushedSteps = this._mergePushedSteps(currMetrics.pushedSteps, [args.pushedStep]);
            }

            await this._cache.hSet(CONVERSATION_METRICS_KEY, args.zdConversationId, JSON.stringify({
                ...currMetrics,
                ...args,
                pushedSteps: mergedPushedSteps,
            }));
        }
        else {
            if (args.pushedStep) {
                const newMetrics = {
                    ...args,
                    pushedSteps: [args.pushedStep]
                };
                delete newMetrics.pushedStep;
                await this._cache.hSet(CONVERSATION_METRICS_KEY, args.zdConversationId, JSON.stringify(newMetrics));
            }
            else {
                await this._cache.hSet(CONVERSATION_METRICS_KEY, args.zdConversationId, JSON.stringify(args));
            }
        }
    }

    /**
     * 缓存会话产品型号 一天
     * @param conversationId 会话id
     * @param productModel 产品型号
     */
    public async saveProductModelToCache(
        conversationId: string,
        productModel: string,
        message: IConversationMessage
    ): Promise<void> {
        await this._cache.setEX(`productModel:${conversationId}`, productModel, 60 * 60 * 24 * 1000);
        await this._cache.hSet(CONVERSATION_CACHE_KEY, message.messageId, JSON.stringify(message));
    }

    public async getProductModelFromCache(conversationId: string): Promise<string | null> {
        return this._cache.get(`productModel:${conversationId}`);
    }

    public async getLastUserMessageFromCache(conversationId: string): Promise<string | null> {
        return this._cache.get(`lastUserMessage:${conversationId}`);
    }

    /**
     * Mark user info as collected in cache (1 day)
     * @param conversationId conversation id
     */
    public async markUserInfoAsCollected(conversationId: string): Promise<void> {
        await this._cache.setEX(`userInfoCollected:${conversationId}`, 'true', 60 * 60 * 24 * 1000);
    }

    /**
     * Check if user info has been collected
     * @param conversationId conversation id
     * @returns whether user info has been collected
     */
    public async checkUserInfoCollected(conversationId: string): Promise<boolean> {
        const infoCollected = await this._cache.get(`userInfoCollected:${conversationId}`);
        return infoCollected === 'true';
    }

    /**
     * 获取会话的转人工请求次数
     * @param conversationId 会话ID
     * @returns 转人工请求次数
     */
    public async getTransferRequestCount(conversationId: string): Promise<number | null> {
        const countStr = await this._cache.get(`transferRequestCount:${conversationId}`);
        if (countStr) {
            return parseInt(countStr, 10);
        }
        return null;
    }

    /**
     * 增加会话的转人工请求次数
     * @param conversationId 会话ID
     * @returns 更新后的请求次数
     */
    public async incrementTransferRequestCount(conversationId: string): Promise<number> {
        const currentCount = await this.getTransferRequestCount(conversationId) ?? 0;
        const newCount = currentCount + 1;
        await this._cache.setEX(`transferRequestCount:${conversationId}`, newCount.toString(), 60 * 60 * 24); // 24小时过期
        return newCount;
    }

    /**
     * 标记会话是否为内部测试，并设置超时时间。
     * @param conversationId 会话ID
     * @param isTest 是否为测试会话
     * @param ttlSeconds 缓存有效时间（秒），默认为 3600 秒 (1小时)
     */
    public async markConversationAsInternalTest(
        conversationId: string
    ): Promise<void> {
        await this._cache.setEX(`${INTERNAL_TEST_CONVERSATION_CACHE_PREFIX}${conversationId}`, '1', 3600 * 1000);
    }

    private async _checkIfInternalTest(conversationId: string): Promise<number> {
        const cachedValue = await this._cache.get(`${INTERNAL_TEST_CONVERSATION_CACHE_PREFIX}${conversationId}`);
        return cachedValue === '1' ? 1 : 0;
    }

    /**
     * 更新会话最新消息时间
     * @param conversationId 会话ID
     * @param timestamp 时间戳
     */
    public async updateLastMessageTime(conversationId: string, timestamp: number): Promise<void> {
        await this._cache.setEX(`lastMessageTime:${conversationId}`, timestamp.toString(), 60 * 60 * 24); // 24小时过期
    }

    /**
     * 获取会话最新消息时间
     * @param conversationId 会话ID
     * @returns 最新消息时间戳（毫秒）
     */
    public async getLastMessageTime(conversationId: string): Promise<number | null> {
        const timeStr = await this._cache.get(`lastMessageTime:${conversationId}`);
        if (timeStr) {
            return parseInt(timeStr, 10);
        }
        return null;
    }

}
