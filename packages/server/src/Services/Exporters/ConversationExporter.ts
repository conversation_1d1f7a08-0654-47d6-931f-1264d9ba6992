import * as DI from '@reolink-fx/di';
import * as Logs from '@reolink-fx/log';
import * as _ from '@reolink-fx/utils';
import { ExportManager } from '#/Services/Export/ExportManager';
import { ConversationManager } from '#/Services/ConversationManager';
import * as DAO from '#/DAO';
import { ConversationRender } from '#/Views/ConversationRender';
import { UNKNOWN_ERROR } from '#/Services/Export/Export';
import * as RPC from '@reolink-fx/rpc';
import { IGetConversationListArgs } from '../Decls/Conversation';

export interface IExportConversationsArgs extends Omit<IGetConversationListArgs, 'limit' | 'page'> {
    fields?: string[]; // 可选的导出字段列表
}

export interface IConversationExportView {
    id: string;
    intent: string;
    category: string; // 'other' | 'consultation' | 'troubleshooting';
    emotion: string; // 'neutral' | 'negative' | 'positive' | 'extremely_negative';
    isInternalTest: string; // boolean;
    isWrongIntent: string; // boolean;
    agentComments: string;
    isPassToAgent: string; // boolean;
    isCreateTicket: string; // boolean;
    sopId: string;
    completionPercentage?: string; // number; // 这个字段在DAO中不存在，看情况处理
    issueResolved: string; // boolean;
    problemUnresolvedReasons?: string;
    additionalComments: string;
    feedbackCreatedAt: string; // number;
    createdAt: string; // number;
    zdUserId: string;
    // TODO: 根据需要可以添加更多字段，比如用户信息、原始消息等
}

// 定义导出任务的响应
export interface IExportConversationsResponse {
    taskId: string;
}

@DI.Singleton()
export class ConversationExporter {

    @DI.Inject()
    private readonly _exportManager!: ExportManager;

    @Logs.UseLogger('ConversationExporter')
    private readonly _logs!: Logs.ILogger;

    private readonly _conversationMgr = DI.use(ConversationManager);

    private readonly _conversationRender = DI.use(ConversationRender);

    public static readonly conversationFullHeaderDefinition: Record<keyof IConversationExportView, string> = {
        id: '会话ID',
        intent: '意图',
        category: '问题分类',
        emotion: '情绪',
        isInternalTest: '内部测试',
        isWrongIntent: '错误意图',
        agentComments: '客服评论',
        isPassToAgent: '转人工',
        isCreateTicket: '创建工单',
        sopId: 'SOP ID',
        completionPercentage: 'SOP完成度 (%)',
        issueResolved: '问题已解决',
        problemUnresolvedReasons: '问题未解决原因',
        additionalComments: '额外评论',
        feedbackCreatedAt: '反馈时间',
        zdUserId: 'ZD用户ID',
        createdAt: '创建时间',
    };

    public async createConversationTask(
        args: IExportConversationsArgs,
        merchant: number,
        createdBy: number
    ): Promise<string> {
        const res = await this._exportManager.createTask<IExportConversationsArgs>(
            merchant,
            args,
            createdBy
        );
        return res.id;
    }

    private _transformConversationToExportView(
        daoConversation: DAO.Conversations.IEntity,
    ): IConversationExportView {
        const platformView = this._conversationRender.createConversationView(daoConversation, []); // 暂时不传入messages

        const firstPushedStep = platformView.pushedSteps ? platformView.pushedSteps[0] : undefined;
        const completionPercentageFromStep = firstPushedStep?.completionPercentage ?? 0;
        const completionPercentageValue = completionPercentageFromStep;

        return {
            id: String(platformView.id),
            intent: String(platformView.intent),
            category: String(platformView.category),
            emotion: String(platformView.emotion),
            isInternalTest: String(platformView.isInternalTest),
            isWrongIntent: String(platformView.isWrongIntent),
            agentComments: String(platformView.agentComments ?? ''),
            isPassToAgent: String(platformView.isPassToAgent),
            isCreateTicket: String(platformView.isCreateTicket),
            sopId: String(platformView.sopId),
            completionPercentage: String(completionPercentageValue),
            issueResolved: String(platformView.issueResolved),
            problemUnresolvedReasons: platformView.problemUnresolvedReasons ? JSON.stringify(platformView.problemUnresolvedReasons) : '',
            additionalComments: String(platformView.additionalComments ?? ''),
            feedbackCreatedAt: String(platformView.feedbackCreatedAt ?? 0),
            zdUserId: String(platformView.zdUserId ?? ''),
            createdAt: String(platformView.createdAt),
        };
    }

    private async _getConversations(
        args: IExportConversationsArgs,
        page: number,
        limit: number
    ): Promise<RPC.IListResponse<DAO.Conversations.IEntity>> { // 返回DAO实体列表

        const listArgs: IGetConversationListArgs = {
            ...args,
            limit,
            page,
        };

        const [items, totalRows] = await this._conversationMgr.getConversationList(listArgs);

        return {
            items,
            totalRows,
        };
    }

    public async exportRec(
        args: IExportConversationsArgs,
        merchant: number,
        taskId: string,
    ): Promise<void> {
        const PAGE_SIZE = 100; // 每页获取的记录数

        try {
            let conversationsToExport: IConversationExportView[] = [];
            let page = 1;
            let totalRows = 0;

            this._logs.info({
                action: 'export_conversations_start',
                message: 'Starting conversation export process.',
                data: { taskId, merchant, args }
            });

            do {
                const fetchResponse = await this._getConversations(
                    args,
                    page,
                    PAGE_SIZE
                );

                totalRows = fetchResponse.totalRows;
                const fetchedDAOConversations = fetchResponse.items;

                if (fetchedDAOConversations.length > 0) {
                    const transformedItems = fetchedDAOConversations.map(daoConv =>
                        this._transformConversationToExportView(daoConv)
                    );
                    conversationsToExport = conversationsToExport.concat(transformedItems);
                }

                this._logs.debug({
                    action: 'export_conversations_page_fetched',
                    message: `Fetched page ${page} with ${fetchedDAOConversations.length} items. Total so far: ${conversationsToExport.length}/${totalRows}`,
                    data: { taskId, page, fetchedCount: fetchedDAOConversations.length, totalRows }
                });

                page++;
            }
            while (totalRows > conversationsToExport.length);

            await this._exportManager.updateProgressRate(taskId, 50);

            this._logs.info({
                action: 'export_conversations_data_prepared',
                message: `All conversation data prepared for export. Total records: ${conversationsToExport.length}`,
                data: { taskId, totalCount: conversationsToExport.length }
            });

            await this._exportManager.process(
                taskId,
                conversationsToExport,
                this._exportManager.getHeader(ConversationExporter.conversationFullHeaderDefinition, args.fields)
            );

            this._logs.info({
                action: 'export_conversations_success',
                message: 'Conversation export task completed successfully.',
                data: { taskId }
            });

        }
        catch (err: any) {
            this._logs.error({
                action: 'export_conversations_error',
                message: 'Error during conversation export.',
                data: { taskId, error: _.Errors.errorToJson(err) }
            });

            await this._exportManager.failed(
                taskId,
                err,
                err.message ? err.message + (err.context ? JSON.stringify(err.context) : '') : UNKNOWN_ERROR
            );
        }
    }
}
