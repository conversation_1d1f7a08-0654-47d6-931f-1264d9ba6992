import * as DI from '@reolink-fx/di';
import { SSEBroker } from './Clients/SSEBroker';
import * as UUID from '@reolink-fx/uuid';
import * as Decls from './Decls/Chat';
import { ConversationManager } from './ConversationManager';
import { EMessageRole } from './Decls/Conversation';
import { findAndMaskEmails, parseXML, replacePlaceholders } from './Helper/Utils';
import { IBaseOpenAiClient } from './Decls/BaseOpenAiClient';
import { OpenAIClientFactory } from './Clients/OpenAIClientFactory';
import { SOPHelper } from './Helper/SOPHelper';
import { ClaudeAIClient } from './Clients/ClaudeAIClient';
import * as ORM from '@reolink-fx/orm';
import * as Config from '@reolink-fx/config';
import * as _ from '@reolink-fx/utils';
import { ProductSpecHelper } from './Helper/ProductSpecHelper';
import { AggregationPageManager } from './AggregationPageManager';
import { PromptManager } from './PromptManager';
import * as DAO from '#/DAO';

export const CHAT_SELF_INSPECTION_SCENE = 'ai-recall';

export const CHAT_SCENE = 'reolink-zd';

export enum EChatScene {
    CHAT = 'chat',
    CHAT_SELF_INSPECTION_SCENE = 'ai-recall',
    CLASSIFICATION = 'classification',
    TICKET_SUMMARIZATION = 'ticket-summary',
    SEQUENT_REPLY = 'sequent-reply',

    REPLY_INQUIRY = 'reply-inquiry',
    REPLY_TROUBLESHOOTING = 'reply-troubleshooting'
}

export interface ITicketFieldIds {
    'productInquiry': string;
    'troubleshooting': string;
    'orderBusiness': string;
    'feedbackSuggestion': string;
    'complaints': string;
    'informationSecurity': string;
    model: string;
}
@DI.Singleton()
export class ChatSceneManager {

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _openAIFactory = DI.use(OpenAIClientFactory);

    private _openAIClient!: IBaseOpenAiClient;

    private readonly _claudeClient = DI.use(ClaudeAIClient);

    private readonly _articleDAO = ORM.useRepository(DAO.Articles.Entity);

    private readonly _zdModelMapPath = Config.useConfig<string>({
        path: 'zdModelMapPath'
    });

    private readonly _tfIdCfg = Config.useConfig<ITicketFieldIds>({
        path: 'ticketFieldIds',
    });

    private readonly _sopHelper = DI.use(SOPHelper);

    private _zdModelMap: Record<string, string> = {};

    private readonly _productSpecHelper = DI.use(ProductSpecHelper);

    private readonly _sseClient = DI.use(SSEBroker);

    private readonly _conversationMgr = DI.use(ConversationManager);

    private readonly _aggregationPageMgr = DI.use(AggregationPageManager);

    private readonly _sfGen = UUID.useSnowflakeGenerator('default');

    @DI.Initializer()
    protected async _init(): Promise<void> {
        this._openAIClient = this._openAIFactory.getClient();

        const json = await _.File.readJsonFile(this._zdModelMapPath) as Array<{ zd: string; key: string; }>;

        for (const item of json) {
            this._zdModelMap[item.zd] = item.key;
        }
    }

    public async oneRoundChat(
        args: Decls.IChatArgs
    ): Promise<void> {

        const filteredMessage = findAndMaskEmails(args.content);

        //ai回复
        const chatResponse = await this.chatWithScene({
            ...args,
            content: filteredMessage
        });

        //ai回复合规性检查
        await this.chatSelfInspection(
            chatResponse.aiContent,
            args.conversationId,
            chatResponse.messageId,
            filteredMessage
        );
    }

    public async chatWithScene(
        args: Decls.IChatArgs
    ): Promise<Decls.IChatWithSceneResponse> {

        let prompt = '';

        switch (args.scene) {
            case Decls.CHAT_SEARCH_SCENE: {

                const productModel = args.params?.productModel?.toString() ?? '';

                const slugs = await this._aggregationPageMgr.detectSlug(args.content, productModel);

                if (!slugs.length) {
                    return this.handleNoSlugsFound(args);
                }

                prompt = (await this._promptManager.generateAggregationPrompt(args.content, {
                    'slugs': slugs,
                    'productModel': productModel
                }))?.prompt ?? '';
                break;
            }
            case Decls.EChatScene.CLASSIFICATION: {
                prompt = await this._promptManager.generateTicketClassificationPrompt(args.content);
                break;
            }
            default: {
                //根据场景获取prompt
                prompt = this._promptManager.replacePromptUserMessage(
                    args.content,
                    await this._promptManager.getPromptTextByFile(args.scene)
                );
            }
        }

        const messageId = this._sfGen();

        const stream = await this._openAIClient.createStreamChatCompletion([{ role: 'user', content: prompt }]);

        const now = Date.now();

        let finalResponse = '';

        let accumulatedResponse = '';

        for await (const chunk of stream) {

            const text = chunk.choices[0]?.delta?.content;

            const isFinished = chunk.choices[0]?.finish_reason;

            if (text) {

                accumulatedResponse += text;

                // 这里的判断虽然会检查finish_reason,但是只有在text存在时才会进入这个分支
                // 而最后一个chunk通常没有text内容,只有finish_reason
                if (accumulatedResponse.length >= 24 || isFinished) {
                    const response: Decls.IChatResponse = {
                        conversationId: args.conversationId,
                        error: '',
                        message: {
                            content: {
                                contentType: 'text',
                                parts: [accumulatedResponse]
                            },
                            createdAt: now,
                            id: messageId
                        }
                    };

                    //sse消息单播传输
                    await this._sseClient.publishMessage({
                        'channel': Decls.ESSEChannel.AI_CHAT,
                        'subscriberId': args.conversationId,
                        'payload': JSON.stringify(response)
                    });

                    //重置accumulatedResponse
                    accumulatedResponse = '';
                }

                //追加消息
                finalResponse += text;

            }

            //最后一个chunk会给出usage,此时text可能为空,但还需要处理剩余的accumulatedResponse
            if (isFinished) {

                // 如果还有累积的响应,先发送出去
                if (accumulatedResponse.length > 0) {
                    const response: Decls.IChatResponse = {
                        conversationId: args.conversationId,
                        error: '',
                        message: {
                            content: {
                                contentType: 'text',
                                parts: [accumulatedResponse]
                            },
                            createdAt: now,
                            id: messageId
                        }
                    };

                    await this._sseClient.publishMessage({
                        'channel': Decls.ESSEChannel.AI_CHAT,
                        'subscriberId': args.conversationId,
                        'payload': JSON.stringify(response)
                    });
                }

                //消息结束
                await this._sseClient.publishMessage({
                    'channel': Decls.ESSEChannel.AI_CHAT,
                    'subscriberId': args.conversationId,
                    'payload': Decls.CHAT_END_SYMBOL
                });
            }

        }

        //只有chat 才需要记录conversation
        if (args.scene === Decls.CHAT_SEARCH_SCENE) {
            await this._conversationMgr.createConversationMessage({
                messageId: messageId,
                conversationId: args.conversationId,
                message: finalResponse,
                role: EMessageRole.ASSISTANT
            });
        }

        return {
            messageId: messageId,
            conversationId: args.conversationId,
            aiContent: finalResponse
        };

    }

    public async handleNoSlugsFound(args: Decls.IChatArgs): Promise<Decls.IChatWithSceneResponse> {
        await this._sseClient.publishMessage({
            'channel': Decls.ESSEChannel.AI_CHAT_RECALL,
            'subscriberId': args.conversationId,
            'payload': Decls.NOT_FOUND_SLUG_RESPONSE
        });
        //没有找到slug 直接返回done
        await this._sseClient.publishMessage({
            'channel': Decls.ESSEChannel.AI_CHAT_RECALL,
            'subscriberId': args.conversationId,
            'payload': Decls.CHAT_END_SYMBOL
        });

        return {
            messageId: '',
            conversationId: args.conversationId,
            aiContent: ''
        };
    }

    /**
     * AI回复后调用createChatCompletion返回ai回复合规性检查
     */
    public async chatSelfInspection(
        content: string,
        conversationId: string,
        messageId: string,
        inquiry: string
    ): Promise<void> {

        if (conversationId === '') {
            return;
        }

        //根据场景获取prompt
        const prompt = await this._promptManager.generateSelfInspectionPrompt(inquiry, content);

        const response = await this._openAIClient.createChatCompletion([{ role: 'user', content: prompt }]);

        const now = Date.now();

        const text = response.choices[0].message.content;

        if (text) {

            const xml = text.replace(/`/g, '').replace(/[\r\n]/g, '').replace('xml', '');

            const result = parseXML(xml);

            // 给出的结果为pass则跳过处理
            if (result.response?.result[0] === 'pass') {
                return;
            }

            const response: Decls.IChatResponse = {
                conversationId: conversationId,
                error: '',
                message: {
                    content: {
                        contentType: 'text',
                        parts: [text]
                    },
                    createdAt: now,
                    id: messageId
                }
            };

            //sse消息单播传输
            await this._sseClient.publishMessage({
                'channel': Decls.ESSEChannel.AI_CHAT_RECALL,
                'subscriberId': conversationId,
                'payload': JSON.stringify(response)
            });

            //把撤回的信息存入数据库
            await this._conversationMgr.createRecallMessage(conversationId, messageId, content, text);

        }

        //消息结束
        await this._sseClient.publishMessage({
            'channel': Decls.ESSEChannel.AI_CHAT_RECALL,
            'subscriberId': conversationId,
            'payload': Decls.CHAT_END_SYMBOL
        });
    }

    public async chatWithModel(
        content: string,
    ): Promise<string> {

        const response = await this._openAIClient.createChatCompletion([{ role: 'user', content: content }]);

        const text = response.choices[0].message.content;

        if (text) {
            return text;
        }

        return '';
    }

    public async chatWithModelInClaude(
        content: string,
    ): Promise<string> {

        const response = await this._claudeClient.createChatCompletion([{ role: 'user', content: content }]);

        return response;

    }

    //分类提示词生成，注意补充提到/引用的 macro 和 Article
    public async generateTicketClassificationPrompt(message: string): Promise<string> {

        const originalPrompt = await this._promptManager.getPromptTextByFile(EChatScene.CLASSIFICATION);

        const prompt = replacePlaceholders(originalPrompt, {
            'subCategoryProductInquiry': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.productInquiry, 'ProductInquiry'),
            'subCategoryTroubleshooting': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.troubleshooting, 'Troubleshooting'),
            'subCategoryOB': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.orderBusiness, 'Order & Business'),
            'subCategoryFS': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.feedbackSuggestion, 'Feedback & Suggestion'),
            'subCategoryComplaints': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.complaints, 'Complaints'),
            'subCategoryInfoSecurity': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.informationSecurity, 'Information Security'),
            'modelType': Object.values(await this._sopHelper.generateAllModelPrompts()).join('\n'),
        });

        return replacePlaceholders(prompt, {
            'conversation': message,
        });
    }

    public async sequentReply(
        message: string,
        otherInfo: Record<string, any>
    ): Promise<string> {

        // eslint-disable-next-line @reolink/general/disable-json-parse-method
        otherInfo = JSON.parse(message);
        const originalPrompt = await this._promptManager.getPromptTextByFile(EChatScene.SEQUENT_REPLY);

        const prompt = replacePlaceholders(originalPrompt, {
            'conversation': this.transformMessages(otherInfo.messages),
        });

        const reqCategory = otherInfo.category;
        const reqModel = otherInfo.model;
        const reqTopic = otherInfo.topic;

        if (!reqCategory || !message) {
            throw new Error('Invalid data - no category or message');
        }

        const resp1 = await this.chatWithModelInClaude(prompt);

        const resInquiry = this.pickXmlItem(resp1, 'user_inquiry');
        const resSummary = this.pickXmlItem(resp1, 'summary');

        const referenceSOP = await this._sopHelper.getSOPByTitle(reqTopic);

        const referenceModel = await this._productSpecHelper.getProductContent(this._zdModelMap[reqModel]);

        const context = [
            `Order Platform: ${otherInfo.orderPlatform ?? ''}`,
            `Order Id: ${otherInfo.orderId ?? ''}`,
            `Customer Country/Region: ${otherInfo.country ?? ''}`,
            `Software Platform: ${otherInfo.softwarePlatform ?? ''}`
        ];

        const slugs = await this._aggregationPageMgr.detectSlug(message, reqModel ?? '');

        let articleIds: string[] = [];
        if (slugs.length) {
            const x = await this._promptManager.generateAggregationPrompt(message, {
                'slugs': slugs,
                'productModel': reqModel
            });
            if (x) {
                articleIds = x.reference.map(v => v.id);
            }
        }

        const referenceContent = await this.makeReferenceContent(
            referenceModel,
            articleIds,
            referenceSOP ?? undefined,
        );

        const prompt2 = await this.generateReplyPrompt({
            category: reqCategory,
            inquiry: resInquiry,
            summary: resSummary,
            model: reqModel,
            reference: referenceContent,
            context: context.join('\n')
        });

        const resp = await this.chatWithModelInClaude(prompt2);

        const response = `
<category>${reqCategory}</category>
<model>${reqModel}</model>
<topic>${reqTopic}</topic>
<user_inquiry>${resInquiry}</user_inquiry>
<summary>${resSummary}</summary>
<reply>${resp}</reply>`;
        return response;

    }

    /**
     * @param referenceModel
     * @returns
     */
    public async makeReferenceContent(
        referenceModel: string,
        slugArticleIds: string[],
        referenceSOP?: DAO.Sops.Entity,
    ): Promise<string> {

        const ret = [
            `===========Parameters begin==========`,
            referenceModel,
            `===========Parameters end==========`,
            `===========SOP begin==========`,
            `${referenceSOP?.content ?? ''}`,
            `===========SOP end==========`
        ];

        const includesArticleIds = ret.join('\n').match(/articles\/(\d+)/g);

        if (!includesArticleIds) {
            return ret.join('\n');
        }

        const articleIds = includesArticleIds.map((id: string) => parseInt(id.replace(/articles\//g, '')));

        const articles = await this._articleDAO.find({
            where: {
                id: ORM.$in(articleIds)
            }
        });

        const slugArticles = await this._articleDAO.find({
            where: {
                id: ORM.$in(slugArticleIds)
            }
        });

        ret.push(`===========Used in SOP Articles begin==========`);
        ret.push(articles.map(a => `=====article ${a.id} begin=====
${a.title}
${a.body}
=====article ${a.id} end=====`).join('\n'));
        ret.push(`===========Used in SOP Articles end==========`);

        if (slugArticleIds.length) {
            ret.push(`===========Similar Articles begin==========`);
            ret.push(slugArticles.map(a => `=====article ${a.id} begin=====
${a.title}
${a.body}
=====article ${a.id} end=====`).join('\n'));
            ret.push(`===========Similar Articles end==========`);
        }

        return ret.join('\n');
    }

    public transformMessages(messages: Array<{
        role: string;
        content: string;
    }>): string {

        return messages.map(m => `<${m.role}>\n${m.content}\n</${m.role}>`).join('\n');

    }

    public async generateReplyPrompt(
        args: Decls.IReplyPromptArgs
    ): Promise<string> {
        let tplName = EChatScene.REPLY_INQUIRY;
        if (args.category === 'Troubleshooting') {
            tplName = EChatScene.REPLY_TROUBLESHOOTING;
        }

        const tplPrompt = await this._promptManager.getPromptTextByFile(tplName);

        const prompt = tplPrompt.replace('{user_inquiry}', args.inquiry)
            .replace('{product_model}', args.model)
            .replace('{summary}', args.summary)
            .replace('{reference}', args.reference)
            .replace('{context}', args.context)
            .replace('{modelType}', Object.values(await this._sopHelper.generateAllModelPrompts()).join('\n'));

        return prompt;
    }

    public pickXmlItem(result: string, tag: string): string {
        let value = '';
        if (result.includes(`<${tag}>`)) {
            value = result.split(`<${tag}>`)[1].split(`</${tag}>`)[0];
        }
        return value;
    }

    public async generateSelfInspectionPrompt(
        inquiry: string,
        message: string,
    ): Promise<string> {

        const originalPrompt = await this._promptManager.getPromptTextByFile(CHAT_SELF_INSPECTION_SCENE);

        return replacePlaceholders(originalPrompt, {
            'inquiry': inquiry,
            'message': message,
        });
    }

}
