import * as Config from '@reolink-fx/config';
import * as _ from '@reolink-fx/utils';
import * as DI from '@reolink-fx/di';
import { genExcelFormat, replacePlaceholders } from './Helper/Utils';
import { ChatSceneManager } from './ChatSceneManager';
import { DESENSITIZATION_FORMAT, IHeaderMapping, SOP_MATCH_FORMAT, TICKET_FORMAT } from './Decls/Types';
import { IntentMatcher } from './agents/troubleshooting/IntentMatcher';
import { PromptManager } from './PromptManager';
import { LLMService } from './agents/troubleshooting/LLMService';
import { EPromptScene } from './agents/troubleshooting/constants';
export interface IChatInfo {
    description: string;
    standard: string;
    model: string;

}

export interface IBulkTestIntentDetection {
    ticket: string;
    model: string;
    content: string;
    correctSOP: string;
    matchSOP: string;
    isMatch: string;
}

export interface IInfo {
    origin: string;
    result: string;

}

export interface ITicket {
    ticketId: string;
    airesp: string;
    concatenatedContent: string;

}

const transExcelPath = '/home/<USER>/projects/svc-ai-support/desensitization.xlsx';

const resultExcelPath = '/home/<USER>/projects/svc-ai-support/result.xlsx';

const transJsonPath = '/home/<USER>/projects/svc-ai-support/transJson.json';

const resultJsonPath = '/home/<USER>/projects/svc-ai-support/resultJson.json';

const caseExcelPath = '/home/<USER>/projects/svc-ai-support/cases.xlsx';

export class BulkTestChatResponse {

    private readonly _e2jPath = Config.useConfig<string>({
        'path': 'excel2jsonPath'
    });

    private readonly _chatSceneMgr = DI.use(ChatSceneManager);

    private readonly _intentDetector = DI.use(IntentMatcher);

    private readonly _llmService = DI.use(LLMService);

    private readonly _promptManager = DI.use(PromptManager);

    public async excel2Json<T>(
        path: string,
        jsonPath: string,
        format: IHeaderMapping[]
    ): Promise<T[]> {

        const cmd = `${__dirname}/../../../..${this._e2jPath}`;

        const result = await _.File.execAt(
            cmd,
            [
                ...genExcelFormat(format),
                path,
                jsonPath],
            __dirname
        );

        if (result['stderr'] !== '' || !result['stdout'].includes('OK')) {
            console.log(result);
            throw new Error();
        }

        const json = (await _.File.readJsonFile(transJsonPath) as T[]);

        return json;
    }

    public async json2excel(
        format: IHeaderMapping[]
    ): Promise<void> {

        const cmd = `${__dirname}/../../../..${this._e2jPath}`;

        const result = await _.File.execAt(
            cmd,
            [
                ...['--mode', 'json2excel'],
                ...genExcelFormat(format),
                resultJsonPath,
                resultExcelPath],
            __dirname
        );

        if (result['stderr'] !== '' || !result['stdout'].includes('OK')) {
            console.log(result);
            throw new Error();
        }
    }

    public async summaryAgentResponse(): Promise<void> {

        const tickets = await this.excel2Json<ITicket>(
            '/home/<USER>/projects/svc-ai-support/工单客服回复.xlsx',
            transJsonPath,
            TICKET_FORMAT
        );

        const summary: Record<string, ITicket> = {};

        for (const ticket of tickets) {

            if (!summary[ticket.ticketId]) {
                summary[ticket.ticketId] = ticket;
            }
            else {
                summary[ticket.ticketId].concatenatedContent = summary[ticket.ticketId].concatenatedContent.concat(`
                    ======================================================
                    `).concat(ticket.concatenatedContent);
            }
        }

        const newTickets = Object.values(summary);

        await _.File.writeFile(resultJsonPath, JSON.stringify(newTickets));

        await this.json2excel(TICKET_FORMAT);
    }

    public async bulkTestChatResponse(): Promise<void> {

        const chatInfo = await this.excel2Json<IInfo>(
            transExcelPath,
            transJsonPath,
            DESENSITIZATION_FORMAT
        );

        const originalPrompt = await _.File.readTextFile('/home/<USER>/prompt/desensitization');

        for (const info of chatInfo) {

            const prompt = replacePlaceholders(originalPrompt, {
                'user_input': info.origin,
            });

            console.log(prompt);

            const reviewAnswer = await this._chatSceneMgr.chatWithModel(prompt);

            console.log(reviewAnswer);

            info.result = reviewAnswer;

            const keywords = info.result.replace(/<\/?[^>]+(>|$)/g, '')
                .replace('```xml', '')
                .replace('```', '')
                .split('\n')
                .filter(v => !!v);

            //替换脱敏结果
            for (const keyword of keywords) {
                info.origin = info.origin.replace(keyword, '***');
            }
        }

        await _.File.writeFile(resultJsonPath, JSON.stringify(chatInfo));

        await this.json2excel(DESENSITIZATION_FORMAT);

    }

    public async bulkTestIntentDetection(): Promise<void> {

        const needDetectContent = await this.excel2Json<IBulkTestIntentDetection>(
            caseExcelPath,
            transJsonPath,
            SOP_MATCH_FORMAT
        );

        for (const info of needDetectContent) {

            if (info.content === '') {
                continue;
            }

            const systemPrompt = await this._promptManager.generatePromptByScene(
                EPromptScene.DETECT_CATEGORY,
                info.model
            );

            const messages = [{
                'role': 'user',
                'content': info.content
            }];

            const productCategory = await this._llmService.createChatCompletion(systemPrompt, []);

            const state = await this._intentDetector.matchIntent({
                appId: '',
                conversationId: '',
                productCategory: productCategory,
                productModel: info.model,
                category: '',
                emotion: '',
                messages: messages as any,
                response: '',
                status: '',
                doneSteps: [],
                conditions: {},
                chooseCondition: {},
                currentNode: '',
                intent: '',
                routingDecision: '',
                inputType: 'text'
            });

            info.matchSOP = state.intent;

            info.isMatch = info.correctSOP === info.matchSOP ? '是' : '否';

            await _.Async.sleep(1000);

        }

        await _.File.writeFile(resultJsonPath, JSON.stringify(needDetectContent));

        await this.json2excel(SOP_MATCH_FORMAT);
    }

}
