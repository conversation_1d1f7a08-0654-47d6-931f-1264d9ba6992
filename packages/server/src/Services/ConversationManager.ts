import * as ORM from '@reolink-fx/orm';
import * as UUID from '@reolink-fx/uuid';
import * as DAO from '#/DAO';
import * as Decls from './Decls/Conversation';
import { E_CONVERSATION_NOT_FOUND } from '#/Errors';
import * as DI from '@reolink-fx/di';
import { CryptoUtils } from './Helper/CryptoUtils';

export enum EConversationType {
    OTHER = 0,
    CONSULTATION = 1,
    TROUBLESHOOTING = 2
}

@DI.Singleton()
export class ConversationManager {

    private readonly _conversationDAO = ORM.useRepository(DAO.Conversations.Entity);

    private readonly _conversationMessagesDAO = ORM.useRepository(DAO.ConversationMessages.Entity);

    private readonly _recallMessageDAO = ORM.useRepository(DAO.RecallConversationMessages.Entity);

    private readonly _sfGen = UUID.useSnowflakeGenerator('default');

    private readonly _con = ORM.useConnection();

    private readonly _cryptoUtils = DI.use(CryptoUtils);

    private _convertCategoryToNumber(category: string): number {
        switch (category.toLowerCase()) {
            case 'consultation':
                return EConversationType.CONSULTATION;
            case 'troubleshooting':
                return EConversationType.TROUBLESHOOTING;
            default:
                return EConversationType.OTHER;
        }
    }

    public async createConversation(
        args: Decls.ICreateConversationArgs,
        metadata: Record<string, unknown>,
        userId?: string | null
    ): Promise<Decls.ICreateConversationResponse> {

        const conversationId = this._sfGen();

        await this._con.transaction(async (conn) => {

            const conDAO = await conn.getRepository(DAO.Conversations.Entity);

            const conMessageDAO = await conn.getRepository(DAO.ConversationMessages.Entity);

            await DAO.Conversations.Entity.insert(
                conDAO,
                {
                    id: conversationId,
                    isInternalTest: args.isInternalTest ?? 0,
                    zdUserId: args.zdUserId ?? '',
                    zdConversationId: args.zdConversationId ?? '',
                    userId: userId ?? '0',
                    metadata: metadata,
                    createdAt: args.createdAt,
                    modifiedAt: args.createdAt,
                    additionalComments: '',
                    agentComments: '',
                    agentTags: [],
                    category: 0,
                    emotion: 0,
                    feedbackCreatedAt: '0',
                    intent: '',
                    isCreateTicket: 0,
                    isPassToAgent: 0,
                    isWrongIntent: 0,
                    issueResolved: 0,
                    lastStepId: '0',
                    problemUnresolvedReasons: [],
                    pushedSteps: [],
                    sopId: '0'
                }
            );

            const messageId = this._sfGen();

            const encryptedMessage = await this._cryptoUtils.encrypt(messageId, args.content, conn);

            await DAO.ConversationMessages.Entity.insert(
                conMessageDAO,
                {
                    id: messageId,
                    conversationId: conversationId,
                    role: args.role ?? Decls.EMessageRole.USER,
                    message: args.desensitizedContent ?? args.content,
                    createdAt: args.createdAt,
                    encryptedMessage,
                    source: ''
                }
            );

        });

        return {
            conversationId: conversationId
        };
    }

    public async createConversationMessage(
        args: Decls.ICreateConversationMessageArgs
    ): Promise<void> {

        const now = Date.now().toString();

        await this._con.transaction(async (conn) => {

            const conDAO = await conn.getRepository(DAO.Conversations.Entity);

            const conMessageDAO = await conn.getRepository(DAO.ConversationMessages.Entity);

            const encryptedMessage = await this._cryptoUtils.encrypt(args.messageId, args.message, conn);

            await DAO.ConversationMessages.Entity.insert(
                conMessageDAO,
                {
                    id: args.messageId,
                    conversationId: args.conversationId,
                    role: args.role,
                    message: args.message,
                    createdAt: now,
                    encryptedMessage,
                    source: ''
                }
            );

            // 更新conversation使用的tokens
            await conDAO.update({
                id: args.conversationId
            },
            {
                modifiedAt: now
            });
        });
    }

    public async getConversationByZdConversationId(zdConversationId: string): Promise<DAO.Conversations.Entity | null> {
        return this._conversationDAO.findOne({
            where: {
                zdConversationId: zdConversationId
            }
        });
    }

    public async createConversationMessageByZdConversationId(
        args: Decls.ICreateConversationMessageByZdConversationIdArgs
    ): Promise<void> {

        await this._con.transaction(async (conn) => {

            const conDAO = await conn.getRepository(DAO.Conversations.Entity);

            const conMessageDAO = await conn.getRepository(DAO.ConversationMessages.Entity);

            const conversation = await conDAO.findOne({
                where: {
                    zdConversationId: args.zdConversationId
                }
            });

            if (!conversation) {
                throw new E_CONVERSATION_NOT_FOUND();
            }

            const messageId = this._sfGen();

            const encryptedMessage = await this._cryptoUtils.encrypt(messageId, args.message, conn);

            await DAO.ConversationMessages.Entity.insert(
                conMessageDAO,
                {
                    id: messageId,
                    conversationId: conversation.id,
                    role: args.role,
                    message: args.desensitizedMessage,
                    createdAt: args.createdAt,
                    encryptedMessage,
                    source: ''
                }
            );

            const conversationUpdate: ORM.QueryDeepPartialEntity<DAO.Conversations.Entity> = {};

            if (conversation.createdAt > args.createdAt) {
                conversationUpdate.createdAt = args.createdAt;
            }

            if (conversation.modifiedAt < args.createdAt) {
                conversationUpdate.modifiedAt = args.createdAt;
            }

            if (Object.keys(conversationUpdate).length > 0) {
                // 更新conversation使用的tokens
                await conDAO.update({
                    id: conversation.id
                },
                conversationUpdate);
            }
        });
    }

    public async updateConversationByZdConversationId(
        args: Decls.IUpdateConversationByZdConversationIdArgs
    ): Promise<void> {
        await this._conversationDAO.update({
            zdConversationId: args.zdConversationId
        },
        {
            sopId: args.sopId,
            intent: args.intent,
            emotion: args.emotion ?? 0,
            lastStepId: args.lastStepId,
            pushedSteps: args.pushedSteps,
            issueResolved: args.issueResolved,
            isPassToAgent: args.isPassToAgent,
            isCreateTicket: args.isCreateTicket,
            category: args.category ? this._convertCategoryToNumber(args.category) : EConversationType.OTHER
        });
    }

    public async checkConversationIfExist(zdConversationId: string): Promise<boolean> {
        const conversation = await this._conversationDAO.count({
            where: {
                zdConversationId: zdConversationId
            }
        });
        return conversation > 0;
    }

    public async getConversationList(
        args: Decls.IGetConversationListArgs
    ): Promise<[DAO.Conversations.Entity[], number, DAO.ConversationMessages.Entity[]]> {

        const whereCondition: ORM.FindManyOptions<DAO.Conversations.Entity>['where'] = {};

        if (args.id !== undefined) {
            whereCondition.id = args.id;
        }

        if (args.isInternalTest !== undefined) {
            whereCondition.isInternalTest = args.isInternalTest ? 1 : 0;
        }

        if (args.startTime !== undefined && args.endTime !== undefined) {
            whereCondition.createdAt = ORM.$between(args.startTime.toString(), args.endTime.toString());
        }

        if (args.issueResolved !== undefined) {
            whereCondition.issueResolved = args.issueResolved ? 1 : 0;
        }

        if (args.isWrongIntent !== undefined) {
            whereCondition.isWrongIntent = args.isWrongIntent ? 1 : 0;
        }

        if (args.isCreateTicket !== undefined) {
            whereCondition.isCreateTicket = args.isCreateTicket ? 1 : 0;
        }

        if (args.isPassToAgent !== undefined) {
            whereCondition.isPassToAgent = args.isPassToAgent ? 1 : 0;
        }

        if (args.emotion !== undefined) {
            whereCondition.emotion = args.emotion;
        }

        if (args.category !== undefined) {
            whereCondition.category = args.category;
        }

        if (args.sopId !== undefined) {
            whereCondition.sopId = args.sopId;
        }

        const [conversations, totalRows] = await this._conversationDAO.findAndCount({
            where: whereCondition,
            order: {
                id: 'DESC'
            },
            take: args.limit,
            skip: (args.limit && args.page) ? (args.page - 1) * args.limit : undefined
        });

        const conversationIds = conversations.map((conversation) => conversation.id);

        const messages = await this._conversationMessagesDAO.find({
            where: {
                conversationId: ORM.$in(conversationIds)
            },
            order: {
                id: 'ASC'
            }
        });

        return [conversations, totalRows, messages];
    }

    public async markConversation(
        args: Decls.IMarkConversationArgs
    ): Promise<void> {
        await this._conversationDAO.update({
            id: args.id
        }, {
            isWrongIntent: args.isWrongIntent !== undefined ? (args.isWrongIntent ? 1 : 0) : undefined,
            agentTags: args.agentTags,
            agentComments: args.agentComments
        });
    }

    public async createConversationFeedback(
        args: Decls.ICreateConversationFeedbackArgs
    ): Promise<void> {

        const conversation = await this._conversationDAO.findOne({
            where: {
                id: args.conversationId
            }
        });

        if (!conversation) {
            throw new E_CONVERSATION_NOT_FOUND();
        }

        await this._conversationDAO.update(
            {
                id: conversation.id
            },
            {
                issueResolved: args.issueResolved ? 1 : 0,
                problemUnresolvedReasons: args.problemUnresolvedReasons ?? [],
                additionalComments: args.additionalComments ?? '',
                feedbackCreatedAt: Date.now().toString()
            }
        );
    }

    public async createRecallMessage(
        conversationId: string,
        messageId: string,
        content: string,
        reason: string
    ): Promise<void> {
        await this._recallMessageDAO.insert({
            'id': this._sfGen(),
            'conversationId': conversationId,
            'messageId': messageId,
            'content': content,
            'reason': reason,
            'createdAt': Date.now().toString()
        });
    }

}
