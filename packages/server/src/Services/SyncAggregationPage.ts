import * as DI from '@reolink-fx/di';
import { SupportSearchClient } from './Clients/SupportSearchClient';
import * as DAO from '#/DAO';
import * as UUID from '@reolink-fx/uuid';
import * as ORM from '@reolink-fx/orm';
import { Article2Vector } from './Vector/Articles2vector';
import * as <PERSON><PERSON> from 'cron';
import * as Config from '@reolink-fx/config';
import * as _ from '@reolink-fx/utils';
import * as Log from '@reolink-fx/log';
import { EAggregationPageItemTypeInt, IArticleVector } from './Decls/AggregationPage';
import { AggregationPageCache } from './AggregationPageCache';
import { SupportResourceClient } from './Clients/SupportResourceClient';
import TurndownService from 'turndown';
import { hash } from 'crypto';

export enum EResourceUpdateType {
    AGGREGATION_PAGE = 1,
    ARTICLE = 2
}

@DI.Singleton()
export class SyncAggregationPage {

    private readonly _sfGen = UUID.useSnowflakeGenerator('default');

    private readonly _aggregationItemDAO = ORM.useRepository(DAO.AggregationPagesItems.Entity);

    private readonly _aggregationTabDAO = ORM.useRepository(DAO.AggregationPagesTabs.Entity);

    private readonly _articleDAO = ORM.useRepository(DAO.Articles.Entity);

    private readonly _vectorDAO = ORM.useRepository(DAO.ArticleVectors.Entity);

    private readonly _recordDAO = ORM.useRepository(DAO.ResourceUpdateRecords.Entity);

    private readonly _conn = ORM.useConnection();

    private readonly _supportSearchClient = DI.use(SupportSearchClient);

    private readonly _article2Vector = DI.use(Article2Vector);

    private readonly _aggregationPageCache = DI.use(AggregationPageCache);

    private readonly _productClient = DI.use(SupportResourceClient);

    private readonly _productDAO = ORM.useRepository(DAO.ProductSpecs.Entity);

    private readonly _logger = Log.useLogger('SyncAggregationPage');

    private readonly _turnDownService = new TurndownService();

    private readonly _cfg: Record<string, {
        cronConfig: string;
        startRun: boolean;
        enable: boolean;
    }> = Config.useConfig({
            path: 'updateCron',
            validation: {
                sop: {
                    cronConfig: 'string',
                    startRun: 'boolean',
                    enable: 'boolean'
                },
                article: {
                    cronConfig: 'string',
                    startRun: 'boolean',
                    enable: 'boolean'
                }
            }
        });

    public syncAggregationPage(): void {

        this._logger.info({
            'action': 'syncAggregationPage',
            'message': 'syncAggregationPage started.'
        });

        const job = new Cron.CronJob(
            this._cfg.article.cronConfig,
            () => {
                _.Async.invokeAsync(async () => {

                    try {

                        this._logger.info({
                            action: 'process syncAggregationPage',
                            message: `聚合页数据更新 开始时间 ${_.DateTime.timeToDateTimeString(Date.now(), '+0800')}`
                        });

                        await this._updateAggregationPage();

                        await this._updateArticle();

                        //每天固定更新 不需要记录上次更新时间
                        await this._updateAllProductSpec();

                        //更新后重新构建聚合页缓存
                        await this._aggregationPageCache.constructAggregationArticleCache();

                        this._logger.info({
                            action: 'process syncAggregationPage',
                            message: `聚合页数据更新结束 结束时间 ${_.DateTime.timeToDateTimeString(Date.now(), '+0800')}`
                        });
                    }
                    catch (e: unknown) {

                        this._logger.error({
                            action: 'syncAggregationPage',
                            message: 'syncAggregationPage error.',
                            data: {
                                error: _.Errors.errorToJson(e)
                            }
                        });

                    }
                });
            },
            null,
            true,
            'Asia/Shanghai',
            undefined,
            this._cfg.article.startRun
        );

        const nextTime = job.nextDate().toJSDate().getTime();

        if (this._cfg.article.enable) {
            job.start();
        }

        this._logger.info({
            action: 'tellNextDate',
            message: '下次执行时间',
            data: {
                ts: nextTime,
                dateTime: _.DateTime.timeToDateTimeString(nextTime, '+0800'),
            }
        });

    }

    /**
     * 获取并更新所有产品信息
     */
    protected async _updateAllProductSpec(): Promise<void> {

        const productItems = await this._aggregationItemDAO.find({
            'where': {
                'type': EAggregationPageItemTypeInt.PRODUCT_INFO
            }
        });

        const productItemIds = productItems.map((item) => item.id);

        const productTabs = await this._aggregationTabDAO.find({
            'where': {
                'itemId': ORM.$in(productItemIds)
            }
        });

        const productKeys = productTabs.map((tab) => tab.resourceId);

        const productEntities: DAO.ProductSpecs.Entity[] = [];

        for (const productKey of productKeys) {

            const productInfos = await this._productClient.getProductSpec(productKey);

            const product = productInfos.products[0];

            if (!product) {
                continue;
            }

            let productSpecs = `The product parameters of ${product.title} |\n`;

            for (const spec of product.items) {
                const value = spec.value.replace('For support, visit https://support.reolink.com/hc/en-us/', '');
                productSpecs += `| ${spec.category} | ${spec.name} | ${value} |\n`;
            }

            productEntities.push({
                id: this._sfGen(),
                product: productKey,
                content: productSpecs
            });
        }

        await DAO.ProductSpecs.Entity.upsert(this._productDAO, productEntities, 'all');

    }

    //分页获取全部文章
    protected async _getAllArticleOnPaging(after: number): Promise<DAO.Articles.Entity[]> {

        const allArticles: DAO.Articles.Entity[] = [];

        const limit = 100;

        let page = 1;

        let listLength = 0;

        do {
            const articles = await this._supportSearchClient.getUpdateArticles({
                after,
                limit,
                page
            });
            allArticles.push(...articles);
            page++;
            listLength = articles.length;
        }
        while (listLength === limit);

        return allArticles;
    }

    /**
     * 更新文章
     */
    protected async _updateArticle(): Promise<void> {

        const now = Date.now().toString();

        const currentArticles = await this._articleDAO.count();

        if (!currentArticles) {
            //获取全部articles
            const articles = await this._getAllArticleOnPaging(0);

            //获取全部向量
            await this._updateVectorByArticles(articles);

        }
        else {
            const updateRecord = await this._recordDAO.findOne({
                'where': {
                    resource: EResourceUpdateType.ARTICLE
                }
            });

            //获取从现在开始追溯到上次更新之后有进行更新的文章
            const articles = await this._getAllArticleOnPaging(parseInt(updateRecord?.updatedAt ?? '0'));

            //更新文章以及向量
            await this._updateVectorByArticles(articles);
        }

        //记录article资源更新时间
        await DAO.ResourceUpdateRecords.Entity.upsert(this._recordDAO, [{
            'id': this._sfGen(),
            'resource': EResourceUpdateType.ARTICLE,
            'updatedAt': now
        }], 'all');

    }

    private async _getVectorByArticle(
        article: DAO.Articles.Entity,
        content: string
    ): Promise<IArticleVector[]> {

        const vector = await this._article2Vector.getArticleEmbedding({
            'id': article.id,
            'title': article.title,
            'content': content
        });

        return vector;
    }

    protected async _updateVectorByArticles(
        articles: DAO.Articles.Entity[]
    ): Promise<void> {

        const now = Date.now().toString();

        for (const article of articles) {

            const content = this._turnDownService.turndown(article.body);

            const articleVector = await this._vectorDAO.findOne({
                select: ['id', 'content'],
                where: {
                    articleId: article.id
                }
            });

            if (!article.deleted && !article.draft) {

                const currentHash = hash('sha256', content);

                //对比content的hash值 如果一致 则直接跳过
                if (articleVector) {

                    const originHash = hash('sha256', articleVector.content);

                    if (originHash === currentHash) {
                        continue;
                    }

                }

                const vector = await this._getVectorByArticle(article, content);

                //更新向量数据
                await DAO.ArticleVectors.Entity.upsert(this._vectorDAO, [{
                    'id': this._sfGen(),
                    'articleId': article.id,
                    'content': content,
                    'createdAt': now,
                    'modifiedAt': now,
                    'vector': vector
                }],
                ['content', 'vector', 'modifiedAt']);

            }

            await DAO.Articles.Entity.upsert(this._articleDAO, [article], 'all');

        }
    }

    /**
     * 更新聚合页
     */
    protected async _updateAggregationPage(): Promise<void> {

        const updateRecord = await this._recordDAO.findOne({
            'where': {
                resource: EResourceUpdateType.AGGREGATION_PAGE
            }
        });

        //获取从现在开始追溯到上次更新之后有进行更新的聚合页
        const pages = await this._supportSearchClient.getUpdateAggregationPages(parseInt(updateRecord?.updatedAt ?? '0'));

        await this._updateItemAndTabByPages(pages);

        //设置这次的更新时间
        await DAO.ResourceUpdateRecords.Entity.upsert(
            this._recordDAO,
            {
                'id': this._sfGen(),
                'resource': EResourceUpdateType.AGGREGATION_PAGE,
                'updatedAt': Date.now().toString()
            },
            'all'
        );

    }

    private async _updateItemAndTabByPages(
        pages: DAO.AggregationPages.Entity[]
    ): Promise<void> {

        for (const page of pages) {

            //获取该聚合页的全部item 和 tab
            const response = await this._supportSearchClient.getAggregationItemAndTabByPageId(page.id);

            //事务删除该聚合页的item 和 tab 取最新数据插入
            await this._conn.transaction(async (conn) => {

                //更新聚合页
                const pageDAO = await conn.getRepository(DAO.AggregationPages.Entity);

                await DAO.AggregationPages.Entity.upsert(pageDAO, [page], 'all');

                const itemDAO = await conn.getRepository(DAO.AggregationPagesItems.Entity);

                await itemDAO.delete({
                    pageId: page.id
                });

                if (response.items.length) {
                    await itemDAO.bulkInsert(response.items);
                }

                const tabDAO = await conn.getRepository(DAO.AggregationPagesTabs.Entity);

                await tabDAO.delete({
                    pageId: page.id
                });

                if (response.tabs.length) {
                    await tabDAO.bulkInsert(response.tabs);
                }

            });

        }

    }

}
