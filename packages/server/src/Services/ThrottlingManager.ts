import { E_REACH_PER_DAY_REQUEST_LIMIT } from '#/Errors';
import * as Metrics from '@reolink-fx/metrics';
import * as DI from '@reolink-fx/di';
import * as Config from '@reolink-fx/config';
import * as Cache from '@reolink-fx/cache';

export class ThrottlingManager {

    @Cache.UseSharingCache({
        name: 'ip_limit',
        driver: 'redis',
        options: {
            keyNamespace: 'limit:ip:',
            ttl: 3600
        }
    })
    private readonly _ipCounts!: Cache.ISharingCache<number>;

    private readonly _ooc = DI.use(Metrics.OutOfControlReporter);

    // 单个ip每天的最大请求次数
    private readonly _config = Config.useConfig<{
        'maxRequestsPerHour': number;
        'enabled': boolean;
        'whiteList': string[];
    }>({
        'path': 'requestLimit',
        'defaultValue': {
            'maxRequestsPerHour': 20,
            'enabled': true,
            'whiteList': []
        }
    });

    /**
   * 添加一次请求记录
   * @param ipAddress IP 地址
   * @returns 当前 IP 地址的请求次数
   */
    public async addRequest(ipAddress: string): Promise<void> {

        if (!this._config.enabled) {
            return;
        }

        let count = await this._ipCounts.get(ipAddress) ?? 0;

        if (count === false) {
            await this._ipCounts.set(ipAddress, 1);
            return;
        }

        if (this.isOverLimit(count)) {
            //白名单跳过
            if (this._config.whiteList.includes(ipAddress)) {
                return;
            }
            //上报
            this._ooc.report({
                'module': 'ThrottlingManager',
                'problem': 'reach_limit',
                'cause': `ip: ${ipAddress} has reached the limit: ${this._config.maxRequestsPerHour}.`
            });
            throw new E_REACH_PER_DAY_REQUEST_LIMIT({
                'ip': ipAddress
            });
        }
        await this._ipCounts.set(ipAddress, ++count);
    }

    /**
   * 检查给定 IP 地址的请求是否超过限制
   * @param ipAddress IP 地址
   * @returns 如果超过限制则返回 true，否则返回 false
   */
    public isOverLimit(count: number): boolean {
        return count !== undefined && count > this._config.maxRequestsPerHour;
    }

}
