import * as DAO from '#/DAO';
import * as ORM from '@reolink-fx/orm';
import * as UUID from '@reolink-fx/uuid';
import * as Decls from './Decls/AggregationPage';
import * as _ from '@reolink-fx/utils';
import * as Logs from '@reolink-fx/log';
import * as DI from '@reolink-fx/di';
import { SupportResourceClient } from './Clients/SupportResourceClient';

@DI.Singleton()
export class AggregationPageCache {

    private readonly _aggregationPageDAO = ORM.useRepository(DAO.AggregationPages.Entity);

    private readonly _aggregationItemDAO = ORM.useRepository(DAO.AggregationPagesItems.Entity);

    private readonly _aggregationTabDAO = ORM.useRepository(DAO.AggregationPagesTabs.Entity);

    private readonly _vectorDAO = ORM.useRepository(DAO.ArticleVectors.Entity);

    private readonly _articleDAO = ORM.useRepository(DAO.Articles.Entity);

    private readonly _sfGen = UUID.useSnowflakeGenerator('default');

    private readonly _productClient = DI.use(SupportResourceClient);

    private readonly _productDAO = ORM.useRepository(DAO.ProductSpecs.Entity);

    private _aggregationSlugDescriptionMap: Record<string, string> = {};

    private _articleId2ArticleVector: Record<string, Decls.IArticleVector[]> = {};

    private _slug2ArticleId: Record<string, string[]> = {};

    private _slug2AggregationPage: Record<string, Decls.IAggregationPage> = {};

    //一个slug 可能对应多个 产品参数
    private _slug2ProductSpecs: Record<string, string[]> = {};

    private readonly _logger = Logs.useLogger('AggregationPageCache');

    @DI.Initializer()
    protected _init(): void {
        _.Async.invokeAsync(async () => this.constructAggregationArticleCache());
    }

    public async constructAggregationArticleCache(): Promise<void> {

        const start = Date.now();

        this._logger.info({
            'action': 'constructAggregationArticleCache',
            'message': `constructAggregationArticleCache start. 时间：${_.DateTime.timeToDateTimeString(start, '+0800')}`
        });

        const aggregationPages = await this._aggregationPageDAO.find({
            'where': {
                'status': Decls.EAggregationPageStatusInt.PUBLISHED
            }
        });

        const pageIds: string[] = [];

        for (const page of aggregationPages) {
            this._aggregationSlugDescriptionMap[page.slug] = page.seoMeta.describe;
            pageIds.push(page.id);
        }

        const slug2AggregationPage: Record<string, Decls.IAggregationPage> = {};

        const aggregationArticleRelation: Record<string, string[]> = {};

        const slug2ProductSpecs: Record<string, string[]> = {};

        const pageId2Slug = _.Array.toDict(aggregationPages, 'id');

        //初始化聚合页信息
        for (const page of aggregationPages) {
            slug2AggregationPage[page.slug] = {
                id: page.id,
                slug: page.slug,
                title: page.seoMeta.title,
                describe: page.seoMeta.describe,
                items: []
            };
        }

        const pageRecord = _.Array.toDict(aggregationPages, 'id');

        const aggregationItems = await this._aggregationItemDAO.find({
            'where': {
                'pageId': ORM.$in(pageIds),
                'type': ORM.$in([Decls.EAggregationPageItemTypeInt.SUPPORT_ARTICLE, Decls.EAggregationPageItemTypeInt.PRODUCT_INFO])
            }
        });

        const aggregationTabIds: string[] = [];

        const productItemIds: string[] = [];

        for (const item of aggregationItems) {
            if (item.type === Decls.EAggregationPageItemTypeInt.SUPPORT_ARTICLE) {
                aggregationTabIds.push(item.id);
            }
            if (item.type === Decls.EAggregationPageItemTypeInt.PRODUCT_INFO) {
                productItemIds.push(item.id);
            }

        }

        const articleAggregationTabs = await this._aggregationTabDAO.find({
            'where': {
                'itemId': ORM.$in(aggregationTabIds)
            }
        });

        if (productItemIds.length) {
            const productTabs = await this._aggregationTabDAO.find({
                'where': {
                    'itemId': ORM.$in(productItemIds)
                }
            });

            const productKeys: string[] = [];

            for (const tab of productTabs) {
                productKeys.push(tab.resourceId);
            }

            const products = await this._productDAO.find({
                'where': {
                    'product': ORM.$in(productKeys)
                }
            });

            const productKey2Content = _.Array.toDict(products, 'product');

            for (const tab of productTabs) {

                if (productKey2Content[tab.resourceId]) {
                    //构建 slug 2 product spec 缓存
                    (slug2ProductSpecs[pageId2Slug[tab.pageId]?.slug] ??= []).push(
                        productKey2Content[tab.resourceId].content
                    );
                }
                else {
                    //尝试调接口获取
                    const product = await this._productClient.getProductSpec(tab.resourceId);

                    if (product.products.length) {

                        const specs = product.products[0];

                        let productSpecs = specs.title + ' | ';

                        for (const spec of specs.items) {
                            productSpecs += `${spec.category} | ${spec.name} | ${spec.value} |\n`;
                        }

                        await DAO.ProductSpecs.Entity.upsert(this._productDAO, [{
                            'id': this._sfGen(),
                            'product': tab.resourceId,
                            'content': productSpecs
                        }],
                        'all');
                    }

                }

            }
        }

        const item2ResourceId: Record<string, string> = {};

        const articleId2Content: Record<string, string> = {};

        for (const tab of articleAggregationTabs) {
            item2ResourceId[tab.itemId] = tab.resourceId;
            (aggregationArticleRelation[pageRecord[tab.pageId]?.slug] ??= []).push(tab.resourceId);
        }

        this._slug2ArticleId = aggregationArticleRelation;

        //同步所有文章
        const articleVectors: DAO.ArticleVectors.Entity[] = [];

        const totalRaws = await this._vectorDAO.count();

        let skip = 1;

        do {
            //分页获取
            const articleV = await this._vectorDAO.find({
                'order': {
                    'id': 'ASC'
                },
                'take': 100,
                'skip': (skip - 1) * 100
            });

            skip++;

            articleVectors.push(...articleV);
        }
        while (totalRaws !== articleVectors.length);

        const articles = await this._articleDAO.find({
            'select': ['id', 'deleted', 'draft', 'title']
        });

        const articleRecord: Record<string, DAO.Articles.Entity> = _.Array.toDict(articles, 'id');

        // 使用临时对象存储向量
        const tempArticleVectors: Record<string, Decls.IArticleVector[]> = {};

        for (const entity of articleVectors) {
            //被置为删除/草稿 则跳过
            if (!articleRecord[entity.articleId] ||
                articleRecord[entity.articleId].deleted ||
                articleRecord[entity.articleId].draft) {
                continue;
            }
            (tempArticleVectors[entity.articleId] ??= []).push(...entity.vector);
        }

        // 最后统一赋值给成员变量
        this._articleId2ArticleVector = tempArticleVectors;

        //初始化 slug 对应的 article vector
        for (const entity of articleVectors) {
            articleId2Content[entity.articleId] = entity.content;
        }

        for (const tab of articleAggregationTabs) {
            if (!articleRecord[tab.resourceId]) {
                continue;
            }
            slug2AggregationPage[pageRecord[tab.pageId]?.slug]?.items.push({
                'id': articleRecord[tab.resourceId].id,
                'title': articleRecord[tab.resourceId].title,
                'content': articleId2Content[item2ResourceId[tab.itemId]] ?? '',
            });
        }

        //构建完成 替换成员缓存
        this._slug2AggregationPage = slug2AggregationPage;
        this._slug2ProductSpecs = slug2ProductSpecs;

        const end = Date.now();

        this._logger.info({
            'action': 'constructAggregationArticleCache',
            'message': `constructAggregationArticleCache end. 花费时间：${(end - start) / 1000} 秒`
        });

    }

    public getAggregationPageDescribe(slug: string): string {

        return this._aggregationSlugDescriptionMap[slug];
    }

    public getAggregationPageDescribes(): string {

        let slugDesc = '';

        for (const slug in this._aggregationSlugDescriptionMap) {
            slugDesc += `- ${slug}: ${this._aggregationSlugDescriptionMap[slug]}\n`;
        }

        return slugDesc;
    }

    public getAggregationPageVector(
        slug: string
    ): Decls.IArticleVector[] {

        const articleIds = this._slug2ArticleId[slug];

        const vectors: Decls.IArticleVector[] = [];

        if (articleIds?.length) {
            for (const articleId of articleIds) {
                if (this._articleId2ArticleVector[articleId]) {
                    vectors.push(...this._articleId2ArticleVector[articleId]);
                }
            }
        }

        return vectors;
    }

    public getAggregationPage(
        slug: string
    ): Decls.IAggregationPage {

        return this._slug2AggregationPage[slug];
    }

    public getProductSpecsBySlug(
        slug: string
    ): string[] {

        return this._slug2ProductSpecs[slug];
    }

}
