import * as Rpc from '@reolink-fx/rpc';
import * as ZdSOP from '@reolink-services/zendesk-sop';
import * as ORM from '@reolink-fx/orm';
import * as DAO from '#/DAO';
import * as Config from '@reolink-fx/config';
import * as <PERSON><PERSON> from 'cron';
import * as Logs from '@reolink-fx/log';
import * as _ from '@reolink-fx/utils';
import * as $YAML from 'yaml';
import { IMacro } from '@reolink-services/zendesk-sop/lib/SOP';

export interface IExportSOP {
    title: string;
    description: string;
    tasks: IExportTask[];
}

interface IExportTask {
    title: string;
    description: string;
    comment: string;
    onDone?: IExportAction;
    onPending?: IExportAction;
    onSkip?: IExportAction;
}

interface IExportAction {
    applyMacro: IMacro[];
    addTags: string[];
    removeTags: string[];
}

export interface ISopSaveView {

    id: number;
    key: string;
    title: string;
    ticketFieldId: string;

    tasks: Array<{
        title: string;
        comment: string;
        description: string;

    }>;
    content: string;

}

export class SyncSOP {

    private readonly _sopDAO = ORM.useRepository(DAO.Sops.Entity);

    private readonly _logs = Logs.useLogger('SyncSOP');

    private readonly _client = Rpc.useClient<ZdSOP.IAPISet>({
        service: ZdSOP.SERVICE_NAME
    });

    private readonly _cfg: Record<string, {
        cronConfig: string;
        startRun: boolean;
        enable: boolean;
    }> = Config.useConfig({
            path: 'updateCron',
            validation: {
                sop: {
                    cronConfig: 'string',
                    startRun: 'boolean',
                    enable: 'boolean'
                },
                article: {
                    cronConfig: 'string',
                    startRun: 'boolean',
                    enable: 'boolean'
                }
            }
        });

    public async getSOPs(): Promise<ZdSOP.SOP.IAllSOPView[]> {

        const ticketFieldIds = (await this._client.apis.getTicketFieldList()).items.filter(v => v.accountKey === 'reolink').map((item) => item.id);

        const sops: ZdSOP.SOP.IAllSOPView[] = [];

        for (const ticketFieldId of ticketFieldIds) {

            if (ticketFieldId.length === 0) {
                continue;
            }

            const resp = await this._client.apis.getSopsDetail({ ticketFieldId, accountKey: 'reolink' });

            sops.push(...resp.items);
        }

        return sops;

    }

    public syncSOPVector(): void {

        const job = new Cron.CronJob(
            this._cfg.sop.cronConfig,
            () => {
                _.Async.invokeAsync(async () => {

                    try {

                        this._logs.info({
                            action: 'ProcessSyncSop',
                            message: `sop开始时间 ${_.DateTime.timeToDateTimeString(Date.now(), '+0800')}`
                        });

                        await this.updateSOPVectors();

                        this._logs.info({
                            action: 'ProcessSyncSop',
                            message: `sop向量更新结束，结束时间 ${_.DateTime.timeToDateTimeString(Date.now(), '+0800')}`
                        });
                    }
                    catch (e: unknown) {

                        this._logs.error({
                            action: 'syncSOP',
                            message: 'sync sops error.',
                            data: {
                                error: _.Errors.errorToJson(e)
                            }
                        });

                    }
                });
            },
            null,
            true,
            'Asia/Shanghai',
            undefined,
            this._cfg.sop.startRun
        );

        const nextTime = job.nextDate().toJSDate().getTime();

        if (this._cfg.sop.enable) {
            job.start();
        }

        this._logs.info({
            action: 'tellNextDate',
            message: '下次执行时间',
            data: {
                ts: nextTime,
                dateTime: _.DateTime.timeToDateTimeString(nextTime, '+0800'),
            }
        });

    }

    public async updateSOPVectors(): Promise<void> {

        const sops = await this.getSOPs();

        const allSops: ISopSaveView[] = [];

        this._logs.info({
            action: 'updateSOPs',
            message: 'sop更新开始',
            data: {
                sops: sops.length
            }
        });

        for (const sop of sops) {

            const key = sop.optionKey;
            const title = sop.optionValue;

            if (!key || !title) {
                continue;
            }

            const enSop: ISopSaveView = {
                id: sop.sopId,
                key,
                title,
                tasks: sop.tasks,
                content: this.convertSOPToExportSOP(sop),
                ticketFieldId: sop.ticketFieldId,
            };

            allSops.push(enSop);
            // const vector = await this._sopVector.getSOPVector(enSop);

            // await DAO.SopVectors.Entity.upsert(this._sopVectorDAO, { sopId: sop.sopId, vector }, 'all');

        }

        this._logs.info({
            action: 'updateSOPs',
            message: 'sop更新结束',
            data: {
                sops: allSops.length
            }
        });

        await DAO.Sops.Entity.upsert(this._sopDAO, allSops, 'all');

    }

    public convertSOPToExportSOP(sop: ZdSOP.SOP.IAllSOPView): string {

        const ret: IExportSOP = {
            title: sop.optionValue,
            description: sop.description,
            tasks: sop.tasks.map((task) => ({
                title: task.title,
                description: task.description,
                comment: task.comment,
                onDone: task.actions['en']?.onDone ?? undefined,
                onPending: task.actions['en']?.onPending ?? undefined,
                onSkip: task.actions['en']?.onSkip ?? undefined
            }))
        };

        const doc = new $YAML.Document();

        doc.contents = ret as any;

        const ymlString = doc.toString();
        return ymlString;

    }

}
