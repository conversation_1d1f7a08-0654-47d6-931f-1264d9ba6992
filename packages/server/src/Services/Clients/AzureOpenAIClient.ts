import * as Config from '@reolink-fx/config';
import { AzureOpenAI, OpenAI } from 'openai';
import * as DI from '@reolink-fx/di';
import { Stream } from 'openai/streaming';
import * as Metrics from '@reolink-fx/metrics';
import * as _ from '@reolink-fx/utils';
import { E_CREATE_CHAT_ERROR, E_CREATE_STREAM_CHAT_ERROR, E_GET_EMBEDDING_ERROR } from '#/Errors';
import { IBaseOpenAiClient } from '../Decls/BaseOpenAiClient';
import * as LOG from '@reolink-fx/log';

@DI.Singleton()
export class AzureOpenAIClient implements IBaseOpenAiClient {

    private readonly _ooc = DI.use(Metrics.OutOfControlReporter);

    private readonly _logger = LOG.useLogger('AzureOpenAIClient');

    private readonly _config = Config.useConfig<{
        apiKey: string;
        deployment: string;
        miniDeployment: string;
        embeddingDeployment: string;
        apiVersion: string;
        endpoint: string;
        temperature: number;
    }>({
        'path': 'llmClientOptions.azure'
    });

    private _openai: AzureOpenAI = new AzureOpenAI({
        apiKey: '-',
        deployment: '-',
        apiVersion: '-',
        endpoint: '-'
    });

    private _miniOpenAI: AzureOpenAI = new AzureOpenAI({
        apiKey: '-',
        deployment: '-',
        apiVersion: '-',
        endpoint: '-'
    });

    private _embeddingOpenai: AzureOpenAI = new AzureOpenAI({
        apiKey: '-',
        deployment: '-',
        apiVersion: '-',
        endpoint: '-'
    });

    @DI.Initializer()
    protected _diInit(): void {

        this._openai = new AzureOpenAI({
            apiKey: this._config.apiKey,
            deployment: this._config.deployment,
            apiVersion: this._config.apiVersion,
            endpoint: this._config.endpoint
        });

        this._miniOpenAI = new AzureOpenAI({
            apiKey: this._config.apiKey,
            deployment: this._config.miniDeployment,
            apiVersion: this._config.apiVersion,
            endpoint: this._config.endpoint
        });

        this._embeddingOpenai = new AzureOpenAI({
            apiKey: this._config.apiKey,
            deployment: this._config.embeddingDeployment,
            apiVersion: this._config.apiVersion,
            endpoint: this._config.endpoint
        });
    }

    public async createEmbedding(
        input: string
    ): Promise<OpenAI.CreateEmbeddingResponse> {

        try {
            const response = await this._embeddingOpenai.embeddings.create({
                input: input,
                model: this._config.embeddingDeployment
            });

            return response;
        }
        catch (error) {
            this._ooc.report({
                'module': 'AzureOpenAIClient',
                'problem': '获取向量失败',
                'cause': _.Errors.errorToJson(error)
            });
            throw new E_GET_EMBEDDING_ERROR();
        }
    }

    public async createStreamChatCompletion(
        messages: OpenAI.ChatCompletionMessageParam[]
    ): Promise<Stream<OpenAI.ChatCompletionChunk>> {

        try {
            const response = await this._openai.chat.completions.create({
                messages: messages,
                model: this._config.deployment,
                temperature: this._config.temperature,
                stream: true, // 启用流式返回
            });

            return response;
        }
        catch (error: any) {
            if (error.code === 'content_filter') {
                this._logger.error({
                    'action': 'createChatCompletion',
                    'message': 'create chat completion failed(content filter)',
                    'data': {
                        'error': _.Errors.errorToJson(error),
                        'messages': messages
                    }
                });
            }
            else if (error.code === '429') {
                this._logger.error({
                    'action': 'createChatCompletion',
                    'message': 'create chat completion failed(rate limit)',
                    'data': {
                        'error': _.Errors.errorToJson(error)
                    }
                });
                await _.Async.sleep(2000);
            }
            else {
                this._ooc.report({
                    'module': 'AzureOpenAIClient',
                    'problem': '创建大模型流式会话失败',
                    'cause': _.Errors.errorToJson(error)
                });
            }
            throw new E_CREATE_STREAM_CHAT_ERROR();
        }

    }

    public async createChatCompletion(
        messages: OpenAI.ChatCompletionMessageParam[],
        needJsonResponse: boolean = false
    ): Promise<OpenAI.ChatCompletion> {
        try {
            const response = await this._miniOpenAI.chat.completions.create({
                messages: messages,
                temperature: this._config.temperature,
                model: this._config.deployment,
                response_format: needJsonResponse ? {
                    type: 'json_object'
                } : undefined
            });

            return response;
        }
        catch (error: any) {
            if (error.code === 'content_filter') {
                this._logger.error({
                    'action': 'createChatCompletion',
                    'message': 'create chat completion failed(content filter)',
                    'data': {
                        'error': _.Errors.errorToJson(error),
                        'messages': messages
                    }
                });
            }
            else if (error.code === '429') {
                this._logger.error({
                    'action': 'createChatCompletion',
                    'message': 'create chat completion failed(rate limit)',
                    'data': {
                        'error': _.Errors.errorToJson(error)
                    }
                });
                await _.Async.sleep(2000);
            }
            else {
                this._ooc.report({
                    'module': 'AzureOpenAIClient',
                    'problem': '创建大模型会话失败',
                    'cause': _.Errors.errorToJson(error)
                });
            }
            throw new E_CREATE_CHAT_ERROR();
        }

    }

}
