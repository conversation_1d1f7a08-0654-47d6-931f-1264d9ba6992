import { IBaseOpenAiClient } from '../Decls/BaseOpenAiClient';
import { AzureOpenAIClient } from './AzureOpenAIClient';
import { OpenAIClient } from './OpenAIClient';
import * as Config from '@reolink-fx/config';
import * as DI from '@reolink-fx/di';
export class OpenAIClientFactory {

    private readonly _openAIClient = DI.use(OpenAIClient);

    private readonly _azureOpenAIClient = DI.use(AzureOpenAIClient);

    private readonly _clientType = Config.useConfig<string>({
        'path': 'llmClientOptions.clientType'
    });

    public getClient(): IBaseOpenAiClient {
        if (this._clientType === 'azure') {
            return this._azureOpenAIClient;
        }
        else if (this._clientType === 'openai') {
            return this._openAIClient;
        }
        else {
            throw new Error('Invalid client type');
        }
    }
}
