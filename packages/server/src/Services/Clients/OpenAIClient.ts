import { OpenAI } from 'openai';
import * as Config from '@reolink-fx/config';
import * as Metrics from '@reolink-fx/metrics';
import * as DI from '@reolink-fx/di';
import { Stream } from 'openai/streaming';
import * as _ from '@reolink-fx/utils';
import { IBaseOpenAiClient } from '../Decls/BaseOpenAiClient';
import { E_CREATE_CHAT_ERROR, E_CREATE_STREAM_CHAT_ERROR, E_GET_EMBEDDING_ERROR } from '#/Errors';

@DI.Singleton()
export class OpenAIClient implements IBaseOpenAiClient {

    private readonly _ooc = DI.use(Metrics.OutOfControlReporter);

    private readonly _config = Config.useConfig<{
        apiKey: string;
        url: string;
        chatModel: string;
        embeddingModel: string;
    }>({
        'path': 'llmClientOptions.openai'
    });

    private _openai: OpenAI = new OpenAI({
        'apiKey': '',
        'baseURL': ''
    });

    @DI.Initializer()
    protected _diInit(): void {

        this._openai = new OpenAI({
            'apiKey': this._config.apiKey,
            'baseURL': this._config.url
        });
    }

    public async createEmbedding(
        input: string
    ): Promise<OpenAI.CreateEmbeddingResponse> {

        try {
            const response = await this._openai.embeddings.create({
                input: input,
                model: this._config.embeddingModel
            });

            return response;
        }
        catch (error) {
            this._ooc.report({
                'module': 'OpenAIClient',
                'problem': '获取向量失败',
                'cause': _.Errors.errorToJson(error)
            });
            throw new E_GET_EMBEDDING_ERROR();
        }
    }

    public async createStreamChatCompletion(
        messages: OpenAI.ChatCompletionMessageParam[]
    ): Promise<Stream<OpenAI.ChatCompletionChunk>> {

        try {
            const response = await this._openai.chat.completions.create({
                messages: messages,
                model: this._config.chatModel,
                stream: true, // 启用流式返回
            });

            return response;
        }
        catch (error) {
            this._ooc.report({
                'module': 'OpenAIClient',
                'problem': '创建大模型流式会话失败',
                'cause': _.Errors.errorToJson(error)
            });
            throw new E_CREATE_STREAM_CHAT_ERROR();
        }
    }

    public async createChatCompletion(
        messages: OpenAI.ChatCompletionMessageParam[]
    ): Promise<OpenAI.ChatCompletion> {

        try {
            const response = await this._openai.chat.completions.create({
                messages: messages,
                model: this._config.chatModel
            });

            return response;
        }
        catch (error) {
            this._ooc.report({
                'module': 'OpenAIClient',
                'problem': '创建大模型会话失败',
                'cause': _.Errors.errorToJson(error)
            });
            throw new E_CREATE_CHAT_ERROR();
        }
    }

}
