import * as Http from '@reolink-fx/http';
import * as _ from '@reolink-fx/utils';
import * as DI from '@reolink-fx/di';
import * as Config from '@reolink-fx/config';
import { E_REQUEST_ZENDESK_ERROR, E_SEND_MESSAGE_ERROR, E_TRANSFER_ERROR } from '#/Errors/Zendesk';
import * as LOG from '@reolink-fx/log';

export interface IAuthResponse {
    code: string;
    state?: string;
}

export interface IContent {
    type: 'text' | 'image' | 'file' | 'form';
    markdownText?: string;
    blockChatInput?: boolean;
    actions?: Array<{
        type: string;
        text: string;
        payload?: string;
        metadata?: Record<string, any>;
    }>;
    fields?: Array<{
        type: string;
        name: string;
        label: string;
        placeholder: string;
        required: boolean;
    }>;
}

// 定义接口类型
interface ISwitchboard {
    id: string;
    enabled: boolean;
}

interface ISwitchboardIntegration {
    id: string;
    name: string;
    integrationType: string;
}

interface ICustomIntegration {
    integration: {
        id: string;
    };
}

interface ISwitchboardIntegrationResponse {
    switchboardIntegration: {
        id: string;
    };
}

@DI.Singleton()
export class SunshineConversationClient {

    private readonly _logger = LOG.useLogger('SunshineConversationClient');

    private readonly _config = Config.useConfig<{
        appId: string;
        baseUrl: string;
        keyId: string;
        secret: string;
        avatarUrl: string;
        displayName: string;
        receiveMessageUrl: string;
    }>({
        'path': 'sunshineConversationClientOptions'
    });

    /**
     * 初始化Sunshine对话交换机
     * 该方法会检查是否已存在自定义集成，如果不存在则创建一个新的自定义集成
     * 并将其设置为默认集成
     *
     * @returns Promise<void>
     * @throws Error 当初始化过程失败时抛出错误
     */
    @DI.Initializer()
    protected async _initSwitchBoard(): Promise<void> {
        try {
            this._logger.info({
                action: '_initSwitchBoard',
                message: '开始初始化Sunshine对话交换机'
            });

            // 获取交换机
            const switchboard = await this._getSwitchboard();
            if (!switchboard) {
                this._logger.info({
                    action: '_initSwitchBoard',
                    message: '未找到启用的交换机'
                });
                return;
            }

            // 获取交换机集成
            const switchboardIntegration = await this._getSwitchboardIntegrations(switchboard.id);

            // 检查是否已存在custom类型的集成
            const existingCustomIntegration = this._findCustomIntegration(switchboardIntegration);
            if (existingCustomIntegration) {
                this._logger.info({
                    action: '_initSwitchBoard',
                    message: '已存在custom类型的集成，无需初始化',
                    data: { integrationId: existingCustomIntegration.id }
                });
                return;
            }

            // 获取代理工作区集成ID
            const agentSwitchboardIntegrationId = this._findAgentWorkspaceIntegration(switchboardIntegration);

            if (!agentSwitchboardIntegrationId) {
                this._logger.info({
                    action: '_initSwitchBoard',
                    message: '未找到代理工作区集成'
                });
                return;
            }

            // 创建自定义集成
            const customIntegration = await this._createCustomIntegration();

            // 创建自定义交换机集成
            await this._createCustomSwitchboardIntegration(
                switchboard.id,
                customIntegration.integration.id,
                agentSwitchboardIntegrationId
            );

            this._logger.info({
                action: '_initSwitchBoard',
                message: 'Sunshine对话交换机初始化成功',
                data: {
                    switchboardId: switchboard.id,
                    customIntegrationId: customIntegration.integration.id
                }
            });
        }
        catch (error) {
            this._logger.error({
                action: '_initSwitchBoard',
                message: 'Sunshine对话交换机初始化失败',
                data: { error }
            });
        }
    }

    /**
     * 获取交换机
     * @returns Promise<ISwitchboard | null>
     */
    private async _getSwitchboard(): Promise<ISwitchboard | null> {
        const response = await this._request<{
            switchboards: ISwitchboard[];
        }>(
            `/v2/apps/${this._config.appId}/switchboards`,
            'GET'
        );

        return response.switchboards.find(s => s.enabled) ?? null;
    }

    /**
     * 获取交换机集成
     * @param switchboardId 交换机ID
     * @returns Promise<ISwitchboardIntegration[]>
     */
    private async _getSwitchboardIntegrations(switchboardId: string): Promise<ISwitchboardIntegration[]> {
        const response = await this._request<{
            switchboardIntegrations: ISwitchboardIntegration[];
        }>(
            `/v2/apps/${this._config.appId}/switchboards/${switchboardId}/switchboardIntegrations`,
            'GET'
        );

        return response.switchboardIntegrations;
    }

    /**
     * 查找自定义集成
     * @param integrations 集成列表
     * @returns ISwitchboardIntegration | undefined
     */
    private _findCustomIntegration(integrations: ISwitchboardIntegration[]): ISwitchboardIntegration | undefined {
        return integrations.find(i => i.integrationType === 'custom');
    }

    /**
     * 查找代理工作区集成
     * @param integrations 集成列表
     * @returns string | undefined
     */
    private _findAgentWorkspaceIntegration(integrations: ISwitchboardIntegration[]): string | undefined {
        return integrations.find(i => i.integrationType === 'zd:agentWorkspace')?.id;
    }

    /**
     * 创建自定义集成
     * @returns Promise<ICustomIntegration>
     */
    private async _createCustomIntegration(): Promise<ICustomIntegration> {
        const result = await this._request<ICustomIntegration>(
            `/v2/apps/${this._config.appId}/Integrations`,
            'POST',
            {
                'type': 'custom',
                'displayName': 'reo AI Agent',
                'webhooks': [
                    {
                        'target': this._config.receiveMessageUrl,
                        'triggers': [
                            'conversation:message',
                            'conversation:create'
                        ],
                        'includeFullSource': true,
                        'includeFullUser': true
                    }
                ]
            }
        );
        return result;
    }

    /**
     * 创建自定义交换机集成
     * @param switchboardId 交换机ID
     * @param integrationId 集成ID
     * @param nextIntegrationId 下一个集成ID
     * @returns Promise<ISwitchboardIntegrationResponse>
     */
    private async _createCustomSwitchboardIntegration(
        switchboardId: string,
        integrationId: string,
        nextIntegrationId: string
    ): Promise<ISwitchboardIntegrationResponse> {
        const result = await this._request<ISwitchboardIntegrationResponse>(
            `/v2/apps/${this._config.appId}/switchboards/${switchboardId}/switchboardIntegrations`,
            'POST',
            {
                'name': 'custom-chat',
                'deliverStandbyEvents': true,
                'integrationId': integrationId,
                'nextSwitchboardIntegrationId': nextIntegrationId,
            }
        );
        return result;
    }

    /**
     * 通用请求方法
     * @param path 请求路径
     * @param method 请求方法
     * @param data 请求数据，可选
     * @returns Promise<any> 响应数据
     * @throws Error 当HTTP状态码不在200-299范围内时抛出错误
     */
    protected async _request<T = any>(
        path: string,
        method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
        data?: Record<string, any>
    ): Promise<T> {
        try {
            const requestOptions: any = {
                url: `${this._config.baseUrl}${path}`,
                method,
                headers: {
                    'Authorization': `Basic ${Buffer.from(`${this._config.keyId}:${this._config.secret}`).toString('base64')}`,
                    'Content-Type': 'application/json'
                }
            };

            if (data) {
                requestOptions.data = JSON.stringify(data);
            }

            const response = await Http.hCli.request(requestOptions);

            // 获取HTTP状态码
            const statusCode = response.statusCode;

            // 获取响应内容
            const responseText = (await response.getBuffer()).toString();

            // 解析响应数据
            const responseData = _.String.parseJSON<{
                errors?: Array<{
                    code: string;
                    title: string;
                }>;
                [key: string]: any;
            }>(responseText, {
                onError: (error, json) => {
                    this._logger.error({
                        action: '_request',
                        message: '解析响应结果失败',
                        data: { json, error, data }
                    });
                    return {
                        errors: [{
                            code: 'PARSE_ERROR',
                            title: '解析响应结果失败'
                        }]
                    };
                }
            });

            // 检查HTTP状态码是否在200-299范围内
            if (statusCode < 200 || statusCode >= 300) {
                // 构建错误信息
                const errorMessage = responseData.errors && responseData.errors.length > 0
                    ? responseData.errors.map(err => `${err.code}: ${err.title}`).join('; ')
                    : `HTTP错误: ${statusCode}`;

                this._logger.error({
                    action: '_request',
                    message: '请求失败',
                    data: {
                        path,
                        data,
                        method,
                        statusCode,
                        errors: responseData.errors
                    }
                });

                throw new E_REQUEST_ZENDESK_ERROR().addResponseMetadata({
                    'error': errorMessage
                });
            }

            return responseData as T;
        }
        catch (error) {
            this._logger.error({
                action: '_request',
                message: '请求失败',
                data: { path, error, data }
            });
            throw error;
        }
    }

    /**
     * 发送消息
     * @param appId 应用ID
     * @param userId 用户ID
     * @param message 消息内容
     * @returns Promise<void>
     */
    public async sendMessage(
        appId: string,
        conversationId: string,
        content: IContent,
        destination?: Record<string, any>
    ): Promise<string> {
        try {
            const responseData = await this._request<{ messages: Array<{ id: string; }>; }>(
                `/v2/apps/${appId}/conversations/${conversationId}/messages`,
                'POST',
                {
                    author: {
                        type: 'business',
                        displayName: this._config.displayName,
                        avatarUrl: this._config.avatarUrl
                    },
                    content: content,
                    destination: destination
                }
            );

            return responseData.messages[0].id;
        }
        catch (error) {
            throw new E_SEND_MESSAGE_ERROR().addResponseMetadata({
                'error': error,
                'conversationId': conversationId,
                'content': content
            });
        }
    }

    public async typing(appId: string, conversationId: string, type: 'start' | 'stop'): Promise<void> {
        try {
            await this._request(
                `/v2/apps/${appId}/conversations/${conversationId}/activity`,
                'POST',
                {
                    author: {
                        type: 'business',
                        displayName: this._config.displayName,
                        avatarUrl: this._config.avatarUrl
                    },
                    type: `typing:${type}`
                }
            );
        }
        catch (error) {
            throw new E_SEND_MESSAGE_ERROR().addResponseMetadata({
                'error': error
            });
        }
    }

    public async passControl(
        appId: string,
        conversationId: string,
        switchboardIntegration: string,
        metadata: Record<string, any>
    ): Promise<void> {
        try {
            // 创建转接请求
            const respData = await this._request(
                `/v2/apps/${appId}/conversations/${conversationId}/passControl`,
                'POST',
                {
                    switchboardIntegration,
                    metadata
                }
            );

            this._logger.info({
                action: 'passControl',
                message: '人工客服转接完成',
                data: { respData }
            });
        }
        catch (error) {
            throw new E_TRANSFER_ERROR().addResponseMetadata({
                'error': error
            });
        }
    }
}
