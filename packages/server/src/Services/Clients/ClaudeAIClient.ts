/* eslint-disable @typescript-eslint/naming-convention */
import  { AnthropicBedrock } from '@anthropic-ai/bedrock-sdk';
import { MessageParam } from '@anthropic-ai/sdk/resources/messages';
import * as DI from '@reolink-fx/di';
import * as Config from '@reolink-fx/config';
import * as Metrics from '@reolink-fx/metrics';
import * as _ from '@reolink-fx/utils';

@DI.Singleton()
export class ClaudeAIClient {

    private readonly _ooc = DI.use(Metrics.OutOfControlReporter);

    private readonly _config = Config.useConfig<{
        model: string;
        maxTokens: number;
        temperature: number;
        top_k: number;
        top_p: number;

    }>({
        'path': 'llmClientOptions.claude'
    });

    private readonly _anthropic: AnthropicBedrock = new AnthropicBedrock({});

    public async createChatCompletion(
        messages: MessageParam[]
    ): Promise<string> {
        try {
            const response = await this._anthropic.messages.create({
                messages: messages,
                model: this._config.model,
                max_tokens: this._config.maxTokens,
                temperature: 0.1,
                top_p: 1.0,
                top_k: 250,
            });
            if (response.content[0].type === 'text') {
                return response.content[0].text;
            }
            return '';
        }
        catch (error) {
            this._ooc.report({
                'module': 'ClaudeAIClient',
                'problem': '创建聊天失败',
                'cause': _.Errors.errorToJson(error)
            });
            throw new Error('创建聊天失败');
        }
    }
}
