import * as DI from '@reolink-fx/di';
import * as $S from '@reolink-services/sse-broker';
import * as Rpc from '@reolink-fx/rpc';

@DI.Singleton()
export class SSEBroker {

    private readonly _client = Rpc.useClient<$S.Channel.IAPISet>({
        service: $S.SERVICE_NAME
    });

    /**
     * 注册频道广播消息
     */
    public async registryChannel(args: $S.Channel.IRegistryChannelArgs): Promise<void> {

        await this._client.apis.registryChannel(args);
    }

    /**
     * 发布频道广播消息
     */
    public async publishMessage(args: $S.Channel.IPublishUnicastMessageArgs): Promise<void> {

        await this._client.apis.publishMessageTo(args);
    }
}
