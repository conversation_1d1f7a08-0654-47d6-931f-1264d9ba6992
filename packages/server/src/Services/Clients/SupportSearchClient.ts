import * as DI from '@reolink-fx/di';
import * as Rpc from '@reolink-fx/rpc';
import * as DA<PERSON> from '#/DAO';

export interface IAggregationItemAndTab {

    items: DAO.AggregationPagesItems.Entity[];
    tabs: DAO.AggregationPagesTabs.Entity[];
}

export interface IGetUpdateArticlesArgs {
    'after': number;
    'limit': number;
    'page': number;
}

export interface IAPISet {

    getUpdateArticles(args: IGetUpdateArticlesArgs): DAO.Articles.Entity[];

    getUpdateAggregationPages(args: {
        after: number;
    }): DAO.AggregationPages.Entity[];

    getAggregationItemAndTabByPageId(
        args: {
            pageId: string;
        }
    ): Promise<IAggregationItemAndTab>;

}

@DI.Singleton()
export class SupportSearchClient {

    private readonly _client = Rpc.useClient<IAPISet>({
        service: 'SupportSearch'
    });

    public async getUpdateAggregationPages(after: number): Promise<DAO.AggregationPages.Entity[]> {

        return this._client.apis.getUpdateAggregationPages({
            'after': after
        });
    }

    public async getAggregationItemAndTabByPageId(
        pageId: string
    ): Promise<IAggregationItemAndTab> {

        return this._client.apis.getAggregationItemAndTabByPageId({
            'pageId': pageId
        });
    }

    public async getUpdateArticles(args: IGetUpdateArticlesArgs): Promise<DAO.Articles.Entity[]> {

        return this._client.apis.getUpdateArticles(args);
    }

}
