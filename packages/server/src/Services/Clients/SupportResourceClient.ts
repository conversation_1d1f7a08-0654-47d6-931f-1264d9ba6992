import * as Http from '@reolink-fx/http';
import * as LOG from '@reolink-fx/log';
import * as Config from '@reolink-fx/config';
import * as _ from '@reolink-fx/utils';
import * as DI from '@reolink-fx/di';
import { getLastPathSegment } from '../Helper/Utils';

export interface IProductResult {

    'products': Array<{

        /**
         * 产品名称
         *
         */
        'title': string;

        /**
         * 产品 spec
         *
         */
        'items': IProductSpec[];
    }>;
}

export interface IProductSpec {
    /**
     * 分类名称
     * @type string(1,255)
     */
    'category': string;

    /**
     * sepc 名称
     * @type string(1,255)
     */
    'name': string;

    /**
     * sepc 值
     * @type string(1,255)
     */
    'value': string;
}

interface IGetSearchResultResponse {

    /**
     * 搜索结果链接
     */
    'url': string;

    /**
     * 搜索结果来源类型
     *
     * 热词查询出匹配结果 | 算法纠错后热词匹配出结果 | 算法纠错后热词无匹配结果 | 算法无纠错后热词
     */
    'matchType': 'word-match' | 'algo-match' | 'algo-mismatch' | 'algo-no-word';
}
@DI.Singleton()
export class SupportResourceClient {

    private readonly _supportResourceHost = Config.useConfig<string>({
        'path': 'supportResourceHost',
        'defaultValue': 'apis.reolink.com'
    });

    private readonly _logger = LOG.useLogger('SupportResourceClient');

    public async getProductSpec(
        product: string
    ): Promise<IProductResult> {

        const url = _.URL.createURL({
            'protocol': 'https',
            'host': this._supportResourceHost,
            'path': '/v2/official-site-data-cache/support/product/specs/',
            'query': {
                'product': product,
                'language': 'en',
                'country': 'US'
            }
        });

        try {
            const productInfo = await Http.requestAsJson({
                'method': 'GET',
                'url': url,
                'maxResponseSize': 1024 * 1024 * 2, // 2MiB
                'timeout': 30_000,
                'headers': {},
            });

            return productInfo.data as any;
        }
        catch (error) {
            this._logger.error({
                'action': 'getProductSpec',
                'message': 'get product spec failed',
                'data': {
                    'error': _.Errors.errorToJson(error),
                    'url': url
                }
            });
        }

        return {
            'products': []
        };

    }

    public async getAggregationPageSlug(
        product: string
    ): Promise<string> {

        const url = _.URL.createURL({
            'protocol': 'https',
            'host': this._supportResourceHost,
            'path': '/v2/support-search/search',
            'query': {
                'query': product
            }
        });

        try {
            const response = await Http.requestAsJson({
                'method': 'GET',
                'url': url,
                'maxResponseSize': 1024 * 1024 * 2, // 2MiB
                'timeout': 3000,
                'headers': {},
            });

            const searchResponse: IGetSearchResultResponse = response.data as any;

            //只有匹配slug的情况才返回
            if (searchResponse.matchType === 'word-match' || searchResponse.matchType === 'algo-match') {

                const slug = getLastPathSegment(searchResponse.url);

                return slug;
            }

            return '';
        }
        catch (error) {
            this._logger.error({
                'action': 'getAggregationPageSlug',
                'message': 'get aggregation page slug failed',
                'data': {
                    'error': _.Errors.errorToJson(error),
                    'url': url
                }
            });
        }

        return '';

    }
}
