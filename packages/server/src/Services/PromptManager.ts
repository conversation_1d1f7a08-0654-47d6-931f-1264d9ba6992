import * as DI from '@reolink-fx/di';
import * as Config from '@reolink-fx/config';
import * as _ from '@reolink-fx/utils';
import { E_PROMPT_NOT_FOUND } from '#/Errors';
import { AggregationPageCache } from './AggregationPageCache';
import { replacePlaceholders } from './Helper/Utils';
import { IAggregationPage, IArticleVector } from './Decls/AggregationPage';
import { CHAT_SCENE, EChatScene, ITicketFieldIds } from './Decls/Chat';
import { IBaseOpenAiClient } from './Decls/BaseOpenAiClient';
import { VectorCalculator } from './Vector/VectorCalculator';
import { OpenAIClientFactory } from './Clients/OpenAIClientFactory';
import { SOPHelper } from './Helper/SOPHelper';
import { EPromptScene } from './agents/troubleshooting/constants';

const maxReferenceLength = 32_000;
const maxReferenceItems = 10;

interface IPromptData {
    prompt: string;
    reference: IReferenceItem[];
}

interface IReferenceItem {
    url: string;
    id: string;
    title: string;
}
@DI.Singleton()
export class PromptManager {
    private readonly _promptPath = Config.useConfig<string>({
        'path': 'promptPath'
    });

    private readonly _serverRoot = Config.useConfig<string>({
        'path': 'serverRoot'
    });

    private readonly _vectorCalculator = DI.use(VectorCalculator);

    private readonly _openAIFactory = DI.use(OpenAIClientFactory);

    private _openAIClient!: IBaseOpenAiClient;

    private readonly _aggregationPageCache = DI.use(AggregationPageCache);

    private readonly _tfIdCfg = Config.useConfig<ITicketFieldIds>({
        path: 'ticketFieldIds',
    });

    private readonly _sopHelper = DI.use(SOPHelper);

    @DI.Initializer()
    protected _init(): void {
        this._openAIClient = this._openAIFactory.getClient();
    }

    public async getPromptTextByFile(fileName: string): Promise<string> {
        try {
            const prompt = await _.File.readTextFile(`${this._promptPath}/${fileName}`);
            return prompt;
        }
        catch (error) {
            throw new E_PROMPT_NOT_FOUND().addResponseMetadata({
                message: `prompt 文件 未找到 场景 ${fileName}`,
                error: _.Errors.errorToJson(error)
            });
        }
    }

    public replacePromptUserMessage(
        message: string,
        originalPrompt: string
    ): string {
        return replacePlaceholders(originalPrompt, {
            'message': message
        });
    }

    public replacePromptUserMessageAndAggregationPage(
        message: string,
        productModel: string,
        originalPrompt: string
    ): string {
        const aggregationPage = this._aggregationPageCache.getAggregationPageDescribes();

        return replacePlaceholders(originalPrompt, {
            'message': message,
            'productModel': productModel,
            'aggregationPage': aggregationPage
        });
    }

    public async generateDetectSlugPrompt(
        message: string,
        productModel: string
    ): Promise<string> {
        const originalPrompt = await this.getPromptTextByFile('detect-slug');
        return this.replacePromptUserMessageAndAggregationPage(message, productModel, originalPrompt);
    }

    public async generatePromptByScene(
        scene: string,
        message: string
    ): Promise<string> {
        const originalPrompt = await this.getPromptTextByFile(scene);
        return this.replacePromptUserMessage(message, originalPrompt);
    }

    public async generatePromptByAggregation(
        userContext: string,
        message: string,
        referenceText: string,
    ): Promise<string> {
        const originalPrompt = await this.getPromptTextByFile(CHAT_SCENE);
        return replacePlaceholders(originalPrompt, {
            'userContext': userContext,
            'message': message,
            'referenceText': referenceText
        });
    }

    public async generateChooseIntentPrompt(
        intents: string,
        productCategory: string,
        productModel: string
    ): Promise<string> {

        const originalPrompt = await this.getPromptTextByFile('choose_intent');
        return replacePlaceholders(originalPrompt, {
            'intent': intents,
            'category': productCategory,
            'productModel': productModel
        });
    }

    public async generateTranslateStepPrompt(
        step: string,
        message: string
    ): Promise<string> {
        const originalPrompt = await this.getPromptTextByFile(EPromptScene.TRANSLATE_STEP);
        return replacePlaceholders(originalPrompt, {
            'step': step,
            'message': message
        });
    }

    public async generateDetectCustomerStepStatusPrompt(
        step: string,
        message: string
    ): Promise<string> {
        const originalPrompt = await this.getPromptTextByFile(EPromptScene.DETECT_CUSTOMER_STEP_STATUS);
        return replacePlaceholders(originalPrompt, {
            'step': step,
            'message': message
        });
    }

    public async generateTranslateTextOrDescriptionPrompt(
        message: string,
        description?: string,
    ): Promise<string> {
        const originalPrompt = await this.getPromptTextByFile(EPromptScene.TRANSLATE_TEXT_OR_DESCRIPTION);
        return replacePlaceholders(originalPrompt, {
            'description': description ?? '',
            'message': message ?? ''
        });
    }

    public async generateTranslateTextToUserLanguagePrompt(
        message: string,
        text: string
    ): Promise<string> {
        const originalPrompt = await this.getPromptTextByFile(EPromptScene.TRANSLATE_TEXT_TO_USER_LANGUAGE);
        return replacePlaceholders(originalPrompt, {
            'message': message,
            'text': text
        });
    }

    public async generateSelfInspectionPrompt(
        inquiry: string,
        message: string,
    ): Promise<string> {
        const originalPrompt = await this.getPromptTextByFile('ai-recall');
        return replacePlaceholders(originalPrompt, {
            'inquiry': inquiry,
            'message': message,
        });
    }

    public async generateAggregationPrompt(
        message: string,
        params: Record<string, any>
    ): Promise<IPromptData | null> {

        const slugs = params.slugs as string[];

        const response = await this._openAIClient.createEmbedding(message);

        if (!response.data[0].embedding) {
            return null;
        }

        //表单上下文拼接
        const userContext = Object.entries(params)
            .map(([key, value]) => `${key}: ${value} `)
            .join('\n');

        const v = response.data[0].embedding;

        const aggregationPages: IAggregationPage[] = [];

        const vectors: IArticleVector[] = [];

        const productSpecs: string[] = [];

        for (const slug of slugs) {
            const page = this._aggregationPageCache.getAggregationPage(slug);
            if (page) {
                aggregationPages.push(page);
            }
            const articleVectors = this._aggregationPageCache.getAggregationPageVector(slug);
            if (articleVectors?.length) {
                vectors.push(...articleVectors);
            }
            const specs = this._aggregationPageCache.getProductSpecsBySlug(slug);
            if (specs?.length) {
                productSpecs.push(...specs);
            }
        }

        const matchResponse = this._vectorCalculator.getNearbyArticles(
            v,
            maxReferenceItems,
            vectors,
            aggregationPages
        );

        //TODO 可能获取不到 items

        let referenceText = '';

        const referenceItems: IReferenceItem[] = [];

        if (productSpecs.length) {
            for (const product of productSpecs) {
                referenceText += `
=== START OF PRODUCT SPECS ===
    ${product}
=== END OF PRODUCT SPECS ===
`;
            }
        }

        let articleIndex = 1;

        for (const article of matchResponse.articles) {

            const url = `${this._serverRoot}/hc/en-us/articles/${article.id}/?slug=${matchResponse.articleId2Slug[article.id]}`;

            referenceText += `
=== START OF ARTICLE ${articleIndex}===
    URL: ${url}
    Title: ${article.title ?? ''}
    Content: ${article.content ?? ''}
=== END OF ARTICLE ${articleIndex}===
`;
            referenceItems.push({ url, id: article.id, title: article.title ?? '' });
            if (referenceText.length > maxReferenceLength) {
                break;
            }
            articleIndex++;
        }

        const prompt = await this.generatePromptByAggregation(
            userContext,
            message,
            referenceText
        );

        console.log(prompt);

        return { prompt, reference: referenceItems };
    }

    //分类提示词生成，注意补充提到/引用的 macro 和 Article
    public async generateTicketClassificationPrompt(message: string): Promise<string> {

        const originalPrompt = await this.getPromptTextByFile(EChatScene.CLASSIFICATION);

        const prompt = replacePlaceholders(originalPrompt, {
            'subCategoryProductInquiry': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.productInquiry, 'ProductInquiry'),
            'subCategoryTroubleshooting': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.troubleshooting, 'Troubleshooting'),
            'subCategoryOB': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.orderBusiness, 'Order & Business'),
            'subCategoryFS': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.feedbackSuggestion, 'Feedback & Suggestion'),
            'subCategoryComplaints': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.complaints, 'Complaints'),
            'subCategoryInfoSecurity': await this._sopHelper.generateTFPromptsById(this._tfIdCfg.informationSecurity, 'Information Security'),
            'modelType': Object.values(await this._sopHelper.generateAllModelPrompts()).join('\n'),
        });

        return replacePlaceholders(prompt, {
            'inquiry': '{conversation}',
            'message': message,
        });
    }

    public async generateSummarizationPrompt(): Promise<string> {

        let prompt = await this.getPromptTextByFile(EChatScene.TICKET_SUMMARIZATION);

        //prompt中包含由sop标题拆分出来的多个组

        prompt = replacePlaceholders(prompt, {
            'subCategoryProductInquiry': await this._sopHelper.generateTFPromptsById('', 'ProductInquiry'), //TODO: 补充ticketFieldID配置，尽量动态一点。
            'subCategoryTroubleshooting': await this._sopHelper.generateTFPromptsById('', 'ProductInquiry'),
            'subCategoryOB': await this._sopHelper.generateTFPromptsById('', 'ProductInquiry'),
            'subCategoryFS': await this._sopHelper.generateTFPromptsById('', 'ProductInquiry'),
            'subCategoryComplaints': await this._sopHelper.generateTFPromptsById('', 'ProductInquiry'),
            'subCategoryInfoSecurity': await this._sopHelper.generateTFPromptsById('', 'ProductInquiry'),
            'modelType': Object.values(await this._sopHelper.generateAllModelPrompts()).join('\n'),
        });

        const sopPrompt = await this._sopHelper.getRelateArticleAndMacros(''); //TODO: 补充sopKey

        return prompt + sopPrompt;

    }
}
