import { ISopSaveView } from '../SyncSOP';
import * as DI from '@reolink-fx/di';
import { Article2Vector } from './Articles2vector';
import * as Logs from '@reolink-fx/log';
import * as _ from '@reolink-fx/utils';

export default class SOP2Vector {

    private readonly _articleV = DI.use(Article2Vector);

    private readonly _logs = Logs.useLogger('SOP2Vector');

    public async getSOPVector(sop: ISopSaveView): Promise<any[]> {

        const vectors: any = [];

        for (let idx = 1; idx <= sop.tasks.length; idx++) {

            const task = sop.tasks[idx - 1];

            const prefix = `[ task of SOP: '${sop.title}' (${idx}/${sop.tasks.length})]`;

            await _.tryCatch({
                try: async () => {

                    const vector = await this._articleV.getEmbedding(prefix + JSON.stringify({
                        sopTitle: sop.title,
                        sopKey: sop.key,
                        taskTitle: task.title,
                        taskComment: task.comment,
                        taskDescription: task.description,
                    }));

                    vectors.push({
                        vector,
                        id: sop.id,
                        idx,
                    });
                },
                catch: (e) => {

                    this._logs.error({
                        action: 'getSOPVector',
                        message: 'Request llm to create embedding error.',
                        data: {
                            e: _.Errors.errorToJson(e),
                            sop: {
                                title: sop.title,
                                idx
                            }
                        }
                    });

                }

            });

        }

        return vectors;

    }

}
