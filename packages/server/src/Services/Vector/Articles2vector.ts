import * as DI from '@reolink-fx/di';
import { IArticleVector } from '../Decls/AggregationPage';
import { AzureOpenAIClient } from '../Clients/AzureOpenAIClient';

export class Article2Vector {

    private readonly _openAIClient = DI.use(AzureOpenAIClient);

    private _embeddingCache: Record<string, number[]> = {};

    public async getArticleEmbedding(
        item: {
            id: string;
            title: string;
            content: string;
        }
    ): Promise<IArticleVector[]> {

        const vectors: IArticleVector[] = [];

        // 将 content 切分为不超过 {chunkSize} 个字符 的片段
        const contentChunks = this.splitMarkdown(item.content, 512);

        let idx = 0;

        for (const contentChunk of contentChunks) {
            //${slug}::${item.title}
            const prefix = `[ content piece of ${item.title} (${idx}/${contentChunks.length}) ]\n`;
            try {
                const vector = await this.getEmbedding(prefix + contentChunk);
                if (vector.length == 0) {
                    continue;
                }

                vectors.push({
                    'id': item.id,
                    'idx': idx,
                    'vector': vector
                });
            }
            catch (error) {
                console.error(`Error getting embedding: ${contentChunk}`, error);
                continue;
            }
            idx++;
        }

        return vectors;
    }

    // 获取内容块的 embedding 向量
    public async getEmbedding(content: string): Promise<number[]> {
        const hashKey = this._simpleHash(content);
        if (this._embeddingCache[hashKey]) {
            return this._embeddingCache[hashKey];
        }
        // 调用 BigModel API 获取向量
        const response = await this._openAIClient.createEmbedding(content);
        if (!response.data[0]?.embedding) {
            return [];
        }
        const embedding = response.data[0].embedding;
        this._embeddingCache[hashKey] = embedding;
        return embedding;
    }

    // 自己实现一个简单的 hash 算法
    private _simpleHash(content: string): number {
        const hash = 0;
        if (content.length === 0) return hash;
        return content.split('').reduce((a, b) => {
            a = ((a << 5) - a) + b.charCodeAt(0);
            return a & a;
        }, 0);
    }

    public splitMarkdown(markdownText: string, maxChars: number = 800): string[] {
        // Split the text into paragraphs
        const paragraphs = markdownText.split(/\n\s*\n/g);
        const blocks: string[] = [];
        let currentBlock = '';

        for (const paragraph of paragraphs) {
            // If the paragraph itself exceeds the maximum character count, further split it
            if (paragraph.length > maxChars) {
                // Split the paragraph by sentences, including punctuation marks for both Chinese and English
                const sentences = paragraph.split(/(?<=[.!?。！？])/g);
                for (const sentence of sentences) {
                    if (currentBlock.length + sentence.length <= maxChars) {
                        currentBlock += sentence + ' ';
                    }
                    else {
                        if (currentBlock) {
                            blocks.push(currentBlock.trim());
                        }
                        currentBlock = sentence + ' ';
                    }
                }
            }
            // If adding the paragraph does not exceed the maximum character count, add it to the current block
            else if (currentBlock.length + paragraph.length <= maxChars) {
                currentBlock += paragraph + '\n\n';
            }
            // Otherwise, save the current block and start a new one
            else {
                blocks.push(currentBlock.trim());
                currentBlock = paragraph + '\n\n';
            }
        }

        // Add the last block if there is one
        if (currentBlock) {
            blocks.push(currentBlock.trim());
        }

        return blocks;
    }
}
