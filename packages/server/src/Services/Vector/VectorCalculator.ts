import { IAggregationPage, IArticleVector } from '../Decls/AggregationPage';

interface ISimilarityArticleResult {
    id: string;
    similarity: number;
    title?: string;
    content?: string;
}

interface IMatchArticleResult {
    articles: ISimilarityArticleResult[];
    articleId2Slug: Record<string, string>;
}

export class VectorCalculator {
    private readonly _similarityMethod: 'cosine' | 'distance' = 'distance'; // or "distance"

    // 余弦夹角相似度
    private _getCosineSimilarity(v1: number[], v2: number[]): number {
        const len1 = v1.length;
        const len2 = v2.length;
        if (len1 !== len2) {
            return 0;
        }

        let dotProduct = 0;
        let norm1 = 0;
        let norm2 = 0;

        for (let i = 0; i < len1; i++) {
            dotProduct += v1[i] * v2[i];
            norm1 += v1[i] ** 2;
            norm2 += v2[i] ** 2;
        }

        norm1 = Math.sqrt(norm1);
        norm2 = Math.sqrt(norm2);

        if (norm1 === 0 || norm2 === 0) {
            return 0;
        }

        return dotProduct / (norm1 * norm2);
    }

    // 欧式距离相似度
    private _getDistance(v1: number[], v2: number[]): number {
        const len1 = v1.length;
        const len2 = v2.length;
        if (len1 !== len2) {
            return 0;
        }

        let sum = 0;
        for (let i = 0; i < len1; i++) {
            sum += Math.pow(v1[i] - v2[i], 2);
        }
        return Math.sqrt(sum);
    }

    public getNearbyArticles(
        v: number[],
        k: number,
        db: IArticleVector[],
        pages: IAggregationPage[]
    ): IMatchArticleResult {
        if (!v || k <= 0) {
            return {
                articles: [],
                articleId2Slug: {}
            };
        }
        let similarities: ISimilarityArticleResult[] = [];
        if (this._similarityMethod === 'distance') {
            similarities = db.map(item => ({
                id: item.id,
                similarity: this._getDistance(v, item.vector),
            }));
            similarities = similarities.sort((a, b) => a.similarity - b.similarity);
        }
        else {
            similarities = db.map(item => ({
                id: item.id,
                similarity: this._getCosineSimilarity(v, item.vector),
            }));
            similarities = similarities.sort((a, b) => b.similarity - a.similarity);
        }

        return this._fetchArticles(similarities, k, pages);
    }

    private _fetchArticles(
        similarities: ISimilarityArticleResult[],
        k: number,
        pages: IAggregationPage[]
    ): IMatchArticleResult {

        const result: Record<string, number> = {};

        const articleId2Slug: Record<string, string> = {};

        for (const t of similarities) {
            if (result[t.id]) {
                continue;
            }
            result[t.id] = t.similarity;
            k--;
            if (k === 0) {
                break;
            }
        }

        const matchedItems: ISimilarityArticleResult[] = [];

        for (const page of pages) {
            for (const article of page.items) {

                if (result[article.id]) {
                    articleId2Slug[article.id] = page.slug;
                    const similarityResult: ISimilarityArticleResult = {
                        ...article,
                        similarity: result[article.id]
                    };
                    matchedItems.push(similarityResult);
                }
            }
        }

        return {
            'articleId2Slug': articleId2Slug,
            'articles': matchedItems
        };
    }
}
