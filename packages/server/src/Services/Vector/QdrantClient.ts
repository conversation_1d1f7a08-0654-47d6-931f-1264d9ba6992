import { QdrantClient } from '@qdrant/js-client-rest';

export class QdrantClients {

    public async hello(): Promise<void> {

        const client = new QdrantClient({ host: 'localhost', port: 6333 });

        //Prefer low memory footprint with high speed search
        await client.createCollection('{collection_name}', {
            vectors: {
                size: 768,
                distance: 'Cosine',
                on_disk: true,
            },
            quantization_config: {
                scalar: {
                    type: 'int8',
                    always_ram: true,
                },
            },
        });

        await client.query('{collection_name}', {
            query: [0.2, 0.1, 0.9, 0.7],
            params: {
                quantization: {
                    rescore: false,
                },
            },
        });

        //Prefer high precision with low memory footprint
        await client.createCollection('{collection_name}', {
            vectors: {
                size: 768,
                distance: 'Cosine',
                on_disk: true,
            },
            hnsw_config: {
                on_disk: true,
            },
        });

        await client.upsert('{collection_name}', {
            'points': []
        });

        //Prefer high precision with high speed search
        await client.createCollection('{collection_name}', {
            vectors: {
                size: 768,
                distance: 'Cosine',
                on_disk: true,
            },
            quantization_config: {
                scalar: {
                    type: 'int8',
                    always_ram: true,
                },
            },
        });

        await client.query('{collection_name}', {
            query: [0.2, 0.1, 0.9, 0.7],
            params: {
                hnsw_ef: 128,
                exact: false,
            },
            limit: 3,
        });
    }
}
