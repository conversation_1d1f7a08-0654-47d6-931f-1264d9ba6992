import * as DI from '@reolink-fx/di';
import { IContent, SunshineConversationClient } from './Clients/SunshineConversationClient';
import * as LOG from '@reolink-fx/log';
import { IAgentResponse, TroubleShootingWorkFlow } from './agents/troubleshooting/TroubleShootingWorkFlow';
import * as Config from '@reolink-fx/config';
import { EZendeskEventType } from './Decls/Zendesk';
import { ConversationHandler } from './agents/conversation/ConversationHandler';
import { EMessageRole } from './Decls/Conversation';
import { IWebhookMessage } from './Decls/Webhook';
import { AgentController } from './agents/troubleshooting/AgentController';
import { TranslationService } from './agents/troubleshooting/TranslationService';

@DI.Singleton()
export class ChatManager {

    private readonly _sunshineConversationClient = DI.use(SunshineConversationClient);

    private readonly _logger = LOG.useLogger('ChatManager');

    private readonly _troubleShootingWorkFlow = DI.use(TroubleShootingWorkFlow);

    private readonly _conversationHandler = DI.use(ConversationHandler);

    private readonly _agentController = DI.use(AgentController);

    private readonly _translationService = DI.use(TranslationService);

    private readonly _brandId = Config.useConfig<string>({
        'path': 'sunshineConversationClientOptions.brandId'
    });

    /**
     * 处理来自webhook的会话消息
     * @param body webhook请求体
     * @returns 返回处理结果
     */
    public async handleWebhookMessage(body: IWebhookMessage, isTest: boolean = false): Promise<void> {
        console.log('请求体', JSON.stringify(body));
        const appId = body.app.id;
        // 验证事件
        const events = body.events as Array<Record<string, any>> || [];

        for (const event of events) {

            const conversationId = event.payload?.conversation?.id;

            if (isTest) {
                // 标记测试conversation缓存
                await this._conversationHandler.markConversationAsInternalTest(conversationId);
            }
            else {
                // 非测试需验证品牌ID
                if (event.payload?.conversation?.brandId !== this._brandId) {
                    continue;
                }
            }

            //检查事件是否已经被处理过了
            const eventIsProcessed = await this._conversationHandler.checkProcessedEvent(event.id);

            if (eventIsProcessed) {
                this._logger.info({
                    action: 'handleWebhookMessage',
                    message: 'event is processed',
                    data: { event: JSON.stringify(event) }
                });
                continue;
            }

            //缓存记录已经处理的事件
            await this._conversationHandler.saveProcessedEvent(event.id);

            switch (event.type) {
                case EZendeskEventType.CONVERSATION_CREATED:
                    await this._handleConversationCreated(appId, conversationId);
                    break;
                case EZendeskEventType.MESSAGE:
                    if (!(await this._validateEvent(event))) {
                        return ;
                    }
                    // 记录会话最新消息时间
                    await this._conversationHandler.updateLastMessageTime(conversationId, Date.now());

                    if (event.payload?.message?.content?.payload) {
                        await this._handleReplyMessage(appId, event);
                        break;
                    }
                    if (event.payload?.message?.content?.fields) {
                        await this._handleFormMessage(appId, event);
                        break;
                    }
                    await this._handleTextMessage(appId, event);
                    break;
                case EZendeskEventType.READ:
                    break;
                case EZendeskEventType.POSTBACK:
                    await this._handlePostbackMessage(appId, event);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 处理消息并发送响应的基础方法
     * @param appId 应用ID
     * @param conversationId 会话ID
     * @param response 工作流响应
     */
    private async _handleMessageAndSendResponse(
        appId: string,
        conversationId: string,
        response: IAgentResponse
    ): Promise<void> {

        await this._sunshineConversationClient.typing(appId, conversationId, 'stop');

        const extractedData: IContent = {
            type: 'text',
            markdownText: response.response
        };

        if (response.response.length > 0) {
            await this._sunshineConversationClient.sendMessage(appId, conversationId, extractedData);
            return;
        }

        if (response.conditions?.stepId) {
            extractedData.markdownText = response.conditions.step;
            extractedData.actions = response.conditions.actions;
            await this._sunshineConversationClient.sendMessage(appId, conversationId, extractedData);
        }

    }

    private async _handleConversationCreated(
        appId: string,
        conversationId: string
    ): Promise<void> {

        //发送招呼语
        const message: IContent = {
            type: 'text',
            markdownText: `Hello! Welcome to Reolink Support!

To assist you as quickly as possible, please enter your product model in the box below.

If you are unsure about the model of your Reolink device, please refer to this guide to find your model:
[How to Find Model and Serial Numbers on Reolink Devices](https://support.reolink.com/hc/en-us/articles/************-How-to-Find-Model-and-Serial-Numbers-on-Reolink-Devices/)`
        };

        const messageId = await this._sunshineConversationClient.sendMessage(appId, conversationId, message);

        await this._conversationHandler.saveFirstMessageToCache(conversationId, messageId);

        // 发出获取产品型号输入框
        const productModelFrom: IContent = {
            type: 'form',
            blockChatInput: true,
            fields: [
                {
                    type: 'text',
                    name: 'productModel',
                    label: 'Product Model',
                    placeholder: 'Enter your product model',
                    required: true
                }
            ]
        };
        await this._sunshineConversationClient.sendMessage(appId, conversationId, productModelFrom);
    }

    /**
     * 处理文本消息
     */
    private async _handleTextMessage(appId: string, event: Record<string, any>): Promise<void> {

        const content = event.payload?.message?.content?.text;
        const conversationId = event.payload?.conversation?.id;
        const messageId = event.payload?.message?.id;
        const zdUserId = event.payload?.message?.author?.userId;

        if (!content || !conversationId) {
            return;
        }

        const conversationExist = await this._conversationHandler.checkConversationIfExist(conversationId);

        // 保存会话消息到缓存
        await this._conversationHandler.saveMessageToCache({
            zdConversationId: conversationId,
            zdUserId,
            messageId,
            role: EMessageRole.USER,
            content,
            createdAt: Date.now().toString()
        });

        await this._sunshineConversationClient.typing(appId, conversationId, 'start');

        const agentResponse = await this._troubleShootingWorkFlow.chat(
            {
                userMessage: content,
                conversationId,
                appId,
                isNewConversation: !conversationExist,
                inputType: 'text'
            }
        );

        await this._saveAgentResponses(conversationId, zdUserId, messageId, agentResponse);

        await this._handleMessageAndSendResponse(appId, conversationId, agentResponse);
    }

    private async _saveAgentResponses(
        conversationId: string,
        zdUserId: string,
        messageId: string,
        agentResponse: IAgentResponse
    ): Promise<void> {
        if (agentResponse.response.length > 0) {
            await this._conversationHandler.saveMessageToCache({
                zdConversationId: conversationId,
                zdUserId,
                messageId: messageId + '+',
                role: EMessageRole.ASSISTANT,
                content: agentResponse.response,
                createdAt: Date.now().toString()
            });
        }

        if (agentResponse.conditions?.step) {
            await this._conversationHandler.saveMessageToCache({
                zdConversationId: conversationId,
                zdUserId,
                messageId: messageId + '-',
                role: EMessageRole.ASSISTANT,
                content: agentResponse.conditions.step,
                createdAt: Date.now().toString()
            });
        }
    }

    private async _handleReplyMessage(appId: string, event: Record<string, any>): Promise<void> {
        const reply: string = event.payload?.message?.content?.payload;
        const stepId: string = event.payload?.message?.metadata?.stepId;
        const conversationId = event.payload?.conversation?.id;
        const messageId = event.payload?.message?.id;
        const text = event.payload?.message?.content?.text;
        const zdUserId = event.payload?.message?.author?.userId;

        if (!reply || !conversationId) {
            return;
        }

        // 保存会话消息到缓存
        await this._conversationHandler.saveMessageToCache({
            zdConversationId: conversationId,
            zdUserId,
            messageId,
            role: EMessageRole.USER,
            content: text,
            createdAt: Date.now().toString()
        });

        await this._sunshineConversationClient.typing(appId, conversationId, 'start');

        const agentResponse = await this._troubleShootingWorkFlow.chat(
            {
                userMessage: '',
                conversationId,
                appId,
                isNewConversation: false,
                conditions: {
                    stepId,
                    done: reply === 'yes',
                    selectOption: reply,
                    text
                },
                inputType: stepId === 'category' ? 'text' : 'reply'
            }
        );

        await this._saveAgentResponses(conversationId, zdUserId, messageId, agentResponse);

        await this._handleMessageAndSendResponse(appId, conversationId, agentResponse);
    }

    private async _handleFormMessage(appId: string, event: Record<string, any>): Promise<void> {
        const fields = event.payload?.message?.content?.fields;
        const conversationId = event.payload?.conversation?.id;
        const zdUserId = event.payload?.message?.author?.userId;
        if (!fields || !conversationId) {
            return;
        }

        // 处理产品型号表单
        if (fields[0].name === 'productModel') {

            const productModel: string = fields[0].text;
            // 保存产品型号到缓存
            await this._conversationHandler.saveProductModelToCache(
                conversationId,
                productModel,
                {
                    zdConversationId: conversationId,
                    zdUserId,
                    messageId: event.payload?.message?.id,
                    role: EMessageRole.USER,
                    content: productModel,
                    createdAt: Date.now().toString()
                }
            );

            // 发送欢迎语
            const message: IContent = {
                type: 'text',
                markdownText: `Thank you for providing your product model. Please describe your issue in detail.`
            };
            await this._sunshineConversationClient.sendMessage(appId, conversationId, message);
            return;
        }

        // 处理用户信息表单
        if (fields[0].name === 'userName' && fields[1].name === 'userEmail') {
            // 只标记用户信息已收集，不存储具体内容
            await this._conversationHandler.markUserInfoAsCollected(conversationId);

            // 获取最后一条用户消息用于语言检测
            const lastUserMessage = await this._conversationHandler.getLastUserMessageFromCache(conversationId);

            // 需要翻译的文本
            const thankYouText = 'Thanks for your information! We will reply to you via email as soon as possible.';
            const detailsText = 'Remember to share as many details as you can now so our team can provide the most helpful first reply.';

            // 翻译文本
            const [translatedThankYou, translatedDetails] = await this._translationService.translateTexts(
                lastUserMessage ?? '',
                [thankYouText, detailsText]
            );

            // 发送确认消息，然后继续转接流程
            const message: IContent = {
                type: 'text',
                markdownText: translatedThankYou
            };
            await this._sunshineConversationClient.sendMessage(appId, conversationId, message);

            const text: IContent = {
                type: 'text',
                markdownText: translatedDetails
            };

            await this._sunshineConversationClient.sendMessage(appId, conversationId, text);

            // 继续转接流程
            await this._agentController.transfer(appId, conversationId, lastUserMessage ?? '', false);

            return;
        }
    }

    /**
     * 处理回传消息
     */
    private async _handlePostbackMessage(appId: string, event: Record<string, any>): Promise<void> {
        const postback = event.payload?.postback?.payload;
        const stepId = event.payload?.postback?.metadata?.stepId;
        const conversationId = event.payload?.conversation?.id;

        if (!postback || !conversationId) {
            return;
        }

        await this._sunshineConversationClient.typing(appId, conversationId, 'start');

        const response = await this._troubleShootingWorkFlow.chat(
            {
                userMessage: '',
                conversationId,
                appId,
                isNewConversation: false,
                conditions: {
                    stepId,
                    done: postback === 'yes',
                    selectOption: postback
                },
                inputType: 'reply'
            }
        );

        await this._handleMessageAndSendResponse(appId, conversationId, response);
    }

    private async _validateEvent(event: Record<string, any>): Promise<boolean> {
        const message = event.payload?.message;
        const conversationId = event.payload?.conversation?.id;
        // 验证消息作者
        const author = message?.author;
        if (!author || author.type !== 'user') {
            this._logger.debug({
                'action': 'validateEvent',
                'message': '非用户消息，忽略处理'
            });
            return false;
        }

        if (conversationId) {
            const isPass2Agent = await this._conversationHandler.checkPassControlConversation(conversationId);
            if (isPass2Agent) {
                this._logger.debug({
                    'action': 'validateEvent',
                    'message': '人工客服会话，忽略处理'
                });
                return false;
            }
        }

        return true;
    }
}
