import * as ORM from '@reolink-fx/orm';
import * as _ from '@reolink-fx/utils';
import * as DAO from '#/DAO';
import * as DI from '@reolink-fx/di';
import { SopStepManager } from './SopStepManager';
import * as UUID from '@reolink-fx/uuid';
import { E_DUP_SOP } from '#/Errors/Sop';
import { ICreateSopArgs, IGetSopListArgs, IUpdateSopArgs, IViewSop } from '../Decls/Sop';

@DI.Singleton()
export class SopManager {
    private readonly _sopDAO = ORM.useRepository(DAO.SopsV2.Entity);

    private readonly _sceneDAO = ORM.useRepository(DAO.SopScenes.Entity);

    private readonly _sopStepManager = DI.use(SopStepManager);

    private readonly _sfGen = UUID.useSnowflakeGenerator('default');

    public async createSop(
        args: ICreateSopArgs,
        userId: string
    ): Promise<void> {

        const now = Date.now().toString();

        try {
            await this._sopDAO.insert(
                {
                    id: this._sfGen(),
                    description: args.description,
                    intent: args.intent,
                    sceneId: args.sceneId ?? '0',
                    solutions: args.solutions,
                    tags: args.tags,
                    createdBy: userId,
                    modifiedBy: userId,
                    createdAt: now,
                    modifiedAt: now
                }
            );
        }
        catch (error) {
            if (ORM.getQueryHelper().isDuplicatedError(error)) {
                throw new E_DUP_SOP();
            }
            throw error;
        }
    }

    public async updateSop(
        args: IUpdateSopArgs,
        userId: string
    ): Promise<void> {
        const now = Date.now().toString();

        try {
            await this._sopDAO.update(
                { id: args.sopId },
                {
                    intent: args.intent,
                    description: args.description,
                    sceneId: args.sceneId,
                    tags: args.tags,
                    solutions: args.solutions,
                    modifiedBy: userId,
                    modifiedAt: now
                }
            );
        }
        catch (error) {
            if (ORM.getQueryHelper().isDuplicatedError(error)) {
                throw new E_DUP_SOP();
            }
            throw error;
        }
    }

    public async deleteSop(sopId: string): Promise<void> {
        await this._sopDAO.delete({ id: sopId });
    }

    public async getSopById(sopId: string): Promise<IViewSop | null> {
        const sop = await this._sopDAO.findOne({
            where: { id: sopId }
        });

        if (!sop) {
            return null;
        }

        return (await this._constructViewSops([sop]))[0];
    }

    public async getSopList(
        args: IGetSopListArgs
    ): Promise<[IViewSop[], number]> {

        const where: ORM.FindConditions<DAO.SopsV2.Entity> = {};

        if (args.intent) {
            where.intent = ORM.$like(`%${args.intent}%`);
        }

        if (args.ids?.length) {
            where.id = ORM.$in(args.ids);
        }

        const sopQuery = this._sopDAO.createQuery().select().where(where);

        if (args.tag) {
            sopQuery.andWhere(`JSON_CONTAINS(JSON_EXTRACT(tags, '$[*]'), '"${args.tag}"')`);
        }

        if (args.noTags) {
            sopQuery.orWhere('JSON_LENGTH(tags) = 0');
        }

        const sops = await sopQuery
            .skip(args.limit && args.page ? (args.page - 1) * args.limit : undefined)
            .take(args.limit)
            .orderBy('id', 'DESC')
            .getManyAndCount();

        if (!sops[0].length) {
            return [[], 0];
        }

        return [await this._constructViewSops(sops[0]), sops[1]];
    }

    private async _constructViewSops(
        sops: DAO.SopsV2.Entity[],
    ): Promise<IViewSop[]> {

        const stepIds: string[] = [];

        const sceneIds: string[] = [];

        for (const sop of sops) {
            sceneIds.push(sop.sceneId);
            stepIds.push(...sop.solutions.steps.map(step => step.stepId));
        }

        const [scenes, steps] = await Promise.all([
            this._sceneDAO.find({ where: { id: ORM.$in(sceneIds) } }),
            this._sopStepManager.getStepsByIds(stepIds)
        ]);

        const sceneMap = _.Array.toDict(scenes, 'id');
        const stepMap = _.Array.toDict(steps, 'id');

        const viewSops: IViewSop[] = [];

        for (const sop of sops) {

            viewSops.push({
                ...sop,
                scene: sceneMap[sop.sceneId]?.scene ?? '',
                solutions: {
                    steps: sop.solutions.steps.map(step => ({
                        stepId: step.stepId,
                        step: stepMap[step.stepId]?.step ?? '',
                        options: stepMap[step.stepId]?.options ?? {},
                        priority: step.priority,
                    }))
                }
            });
        }

        return viewSops;
    }
}
