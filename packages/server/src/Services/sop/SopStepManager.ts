import * as ORM from '@reolink-fx/orm';
import * as DAO from '#/DAO';
import * as DI from '@reolink-fx/di';
import * as UUID from '@reolink-fx/uuid';
import { E_DUP_STEP, E_STEP_ALREADY_USED } from '#/Errors/Sop';
import { ICreateStepArgs, IUpdateStepArgs, IGetStepListArgs } from '../Decls/Sop';

@DI.Singleton()
export class SopStepManager {
    private readonly _stepDAO = ORM.useRepository(DAO.SopSteps.Entity);

    private readonly _sopDAO = ORM.useRepository(DAO.SopsV2.Entity);

    private readonly _sfGen = UUID.useSnowflakeGenerator('default');

    public async createStep(
        args: ICreateStepArgs,
        userId: string
    ): Promise<void> {

        const now = Date.now().toString();

        try {
            await this._stepDAO.insert(
                {
                    id: this._sfGen(),
                    step: args.step,
                    options: args.options,
                    createdBy: userId,
                    modifiedBy: userId,
                    createdAt: now,
                    modifiedAt: now
                }
            );
        }
        catch (error) {
            if (ORM.getQueryHelper().isDuplicatedError(error)) {
                throw new E_DUP_STEP();
            }
            throw error;
        }
    }

    public async updateStep(
        stepId: string,
        args: IUpdateStepArgs,
        userId: string
    ): Promise<void> {

        try {
            await this._stepDAO.update(
                { id: stepId },
                {
                    step: args.step,
                    options: args.options,
                    modifiedBy: userId,
                    modifiedAt: Date.now().toString()
                }
            );
        }
        catch (error) {
            if (ORM.getQueryHelper().isDuplicatedError(error)) {
                throw new E_DUP_STEP();
            }
            throw error;
        }
    }

    public async deleteStep(stepId: string): Promise<void> {

        const sop = await this._sopDAO
            .createQuery()
            .select(['count(*) as count'])
            .where(`JSON_SEARCH(solutions, 'one', ${stepId}, NULL, '$.steps[*].stepId') IS NOT NULL`)
            .execute();

        if (sop[0].count > 0) {
            throw new E_STEP_ALREADY_USED();
        }

        await this._stepDAO.delete({ id: stepId });
    }

    public async getSteps(
        args: IGetStepListArgs
    ): Promise<[DAO.SopSteps.IEntity[], number]> {
        const result = await this._stepDAO.findAndCount({
            where: args.step
                ? { step: ORM.$like(`%${args.step}%`) }
                : {},
            skip: args.limit && args.page
                ? (args.page - 1) * args.limit
                : undefined,
            take: args.limit,
            order: {
                id: 'DESC'
            }
        });

        return result;
    }

    public async getStepsByIds(stepId: string[]): Promise<DAO.SopSteps.IEntity[]> {
        const results = await this._stepDAO.find({
            where: { id: ORM.$in(stepId) }
        });
        return results;
    }

    public async getStepById(stepId: string): Promise<DAO.SopSteps.IEntity> {
        const result = await this._stepDAO.findOne({
            where: { id: stepId }
        });
        if (!result) {
            throw new Error();
        }
        return result;
    }
}
