import * as ORM from '@reolink-fx/orm';
import * as DAO from '#/DAO';
import * as DI from '@reolink-fx/di';
import * as UUID from '@reolink-fx/uuid';
import { E_DUP_SCENE, E_SCENE_ALREADY_USED } from '#/Errors/Sop';
import { ICreateSceneArgs, IUpdateSceneArgs, IGetSceneListArgs } from '../Decls/Sop';

@DI.Singleton()
export class SopSceneManager {
    private readonly _sceneDAO = ORM.useRepository(DAO.SopScenes.Entity);

    private readonly _sopDAO = ORM.useRepository(DAO.SopsV2.Entity);

    private readonly _sfGen = UUID.useSnowflakeGenerator('default');

    public async createScene(
        args: ICreateSceneArgs,
        userId: string
    ): Promise<void> {
        const now = Date.now().toString();

        try {
            await this._sceneDAO.insert(
                {
                    id: this._sfGen(),
                    scene: args.scene,
                    metadata: {},
                    createdBy: userId,
                    modifiedBy: userId,
                    createdAt: now,
                    modifiedAt: now
                }
            );

        }
        catch (error) {
            if (ORM.getQueryHelper<DAO.SopScenes.Entity>().isDuplicatedError(error)) {
                throw new E_DUP_SCENE();
            }
            throw error;
        }
    }

    public async updateScene(
        args: IUpdateSceneArgs,
        userId: string
    ): Promise<void> {
        try {
            await this._sceneDAO.update(
                { id: args.sceneId },
                {
                    scene: args.scene,
                    metadata: {},
                    modifiedBy: userId,
                    modifiedAt: Date.now().toString()
                }
            );
        }
        catch (error) {
            if (ORM.getQueryHelper().isDuplicatedError(error)) {
                throw new E_DUP_SCENE();
            }
            throw error;
        }
    }

    public async deleteScene(sceneId: string): Promise<void> {
        const sop = await this._sopDAO.findOne({
            'where': {
                'sceneId': sceneId
            }
        });
        if (sop) {
            throw new E_SCENE_ALREADY_USED();
        }
        await this._sceneDAO.delete({ id: sceneId });
    }

    public async getSceneList(args: IGetSceneListArgs): Promise<[DAO.SopScenes.IEntity[], number]> {
        const result = await this._sceneDAO.findAndCount({
            where: args.scene ? { scene: ORM.$like(`%${args.scene}%`) } : {},
            skip: args.limit && args.page ? (args.page - 1) * args.limit : undefined,
            take: args.limit,
            order: {
                id: 'DESC'
            }
        });
        return result;
    }

}
