import * as DI from '@reolink-fx/di';
import { findAndMaskEmails, pickXMLTag } from './Helper/Utils';
import * as Config from '@reolink-fx/config';
import { AggregationPageCache } from './AggregationPageCache';
import { SupportResourceClient } from './Clients/SupportResourceClient';
import { PromptManager } from './PromptManager';
import { IBaseOpenAiClient } from './Decls/BaseOpenAiClient';
import { OpenAIClientFactory } from './Clients/OpenAIClientFactory';

export class AggregationPageManager {
    private readonly _promptManager = DI.use(PromptManager);

    private readonly _aggregationPageCache = DI.use(AggregationPageCache);

    private readonly _detectSlugWay = Config.useConfig<string>({
        'path': 'detectSlugWay',
        'defaultValue': 'support'
    });

    private readonly _supportResourceClient = DI.use(SupportResourceClient);

    private _openAIClient!: IBaseOpenAiClient;

    private readonly _openAIFactory = DI.use(OpenAIClientFactory);

    @DI.Initializer()
    protected _init(): void {
        this._openAIClient = this._openAIFactory.getClient();
    }

    public async detectSlug(content: string, productModel: string): Promise<string[]> {
        const slugs: string[] = [];

        if (this._detectSlugWay === 'support') {
            const slug = await this._supportResourceClient.getAggregationPageSlug(productModel);
            if (slug) {
                slugs.push(slug);
            }
        }
        else {
            const filteredMessage = findAndMaskEmails(content);
            const detectSlugPrompt = await this._promptManager.generateDetectSlugPrompt(filteredMessage, productModel);
            const response = await this._openAIClient.createChatCompletion([{ role: 'user', content: detectSlugPrompt }]);

            const text = response.choices[0].message.content;

            if (text) {
                const slugStr = pickXMLTag(text, 'slug');
                const detectSlugs = slugStr.split(';');
                if (detectSlugs.length) {
                    slugs.push(...detectSlugs);
                }
            }
        }

        const existSlugs: string[] = [];
        for (const slug of slugs) {
            //如果聚合页 & 聚合页向量 & 聚合页描述都存在，则返回
            if (this._aggregationPageCache.getAggregationPageDescribe(slug) &&
                this._aggregationPageCache.getAggregationPageVector(slug) &&
                this._aggregationPageCache.getAggregationPage(slug)) {
                existSlugs.push(slug);
            }
        }
        return existSlugs;
    }
}
