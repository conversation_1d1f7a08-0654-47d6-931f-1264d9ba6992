import 'ts-alias-loader';
import * as Svc from '@reolink-fx/service';
import * as $AISupport from '@reolink-services/ai-support';
import * as DI from '@reolink-fx/di';
import { ClassificationTest } from './Tests/ClassificationTest';
@Svc.ServiceSchema({
    name: `${$AISupport.SERVICE_NAME}-classification`,
})
export default class ServiceAISupport extends Svc.AbstractService {

    private readonly _classificationTest = DI.use(ClassificationTest);

    public async main(): Promise<number> {

        await this._start();

        console.log('service started.');

        await this._classificationTest.testClassification();

        await this._termination.wait();

        await this._stop();

        return 0;
    }
}
