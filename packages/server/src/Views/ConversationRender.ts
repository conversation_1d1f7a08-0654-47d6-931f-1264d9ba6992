import { EConversationCategory, EConversationEmotion, EMessageRole, EProblemUnresolvedReason } from '#/Services/Decls/Conversation';
import * as _ from '@reolink-fx/utils';
import * as $P from '@reolink-services/ai-support.platform-apis';
import * as DAO from '#/DAO';

export class ConversationRender {

    public readonly reasonEnum2Str = _.Enum.createEnumToStringConvertor(EProblemUnresolvedReason);

    public readonly reasonStr2Enum = _.Enum.createStringToEnumConvertor(EProblemUnresolvedReason);

    public readonly categoryEnum2Str = _.Enum.createEnumToStringConvertor(EConversationCategory);

    public readonly categoryStr2Enum = _.Enum.createStringToEnumConvertor(EConversationCategory);

    public readonly emotionEnum2Str = _.Enum.createEnumToStringConvertor(EConversationEmotion);

    public readonly emotionStr2Enum = _.Enum.createStringToEnumConvertor(EConversationEmotion);

    public createConversationView(
        conversation: DAO.Conversations.Entity,
        messages: DAO.ConversationMessages.Entity[]
    ): $P.Conversation.IConversation {

        return {
            ...conversation,
            isWrongIntent: conversation.isWrongIntent ? true : false,
            isPassToAgent: conversation.isPassToAgent ? true : false,
            isCreateTicket: conversation.isCreateTicket ? true : false,
            isInternalTest: conversation.isInternalTest ? true : false,
            agentComments: conversation.agentComments ?? '',
            emotion: this.emotionEnum2Str(conversation.emotion),
            category: this.categoryEnum2Str(conversation.category),
            messages: messages.map((v) => ({
                role: v.role === EMessageRole.ASSISTANT ? 'assistant' : 'user',
                content: v.message,
                createdAt: parseInt(v.createdAt)
            })),
            problemUnresolvedReasons: conversation.problemUnresolvedReasons?.map((v) =>
                this.reasonEnum2Str(v)) ?? [],
            issueResolved: conversation.issueResolved ? true : false,
            additionalComments: conversation.additionalComments ?? '',
            feedbackCreatedAt: parseInt(conversation.feedbackCreatedAt ?? '0'),
            createdAt: parseInt(conversation.createdAt)
        };
    }
}
