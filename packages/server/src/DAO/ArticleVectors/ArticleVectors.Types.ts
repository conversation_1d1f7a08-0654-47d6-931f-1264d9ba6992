/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './ArticleVectors.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IArticleVectorsCreation {

    /**
     * 文章id
     *
     * @field article_id
     * @type bigint unsigned
     * @unique
     */
    articleId: string | ORM.IRawSQL;

    /**
     * 文章内容
     *
     * @field content
     * @type mediumtext
     */
    content: string | ORM.IRawSQL;

    /**
     * 向量创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * 文章向量 id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 向量修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    modifiedAt: string | ORM.IRawSQL;

    /**
     * 文章向量
     *
     * @field vector
     * @type json
     */
    vector: Custom.TVector | ORM.IRawSQL;
}

export {
    IArticleVectorsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IArticleVectorsEntity {

    /**
     * 文章id
     *
     * @field article_id
     * @type bigint unsigned
     * @unique
     */
    articleId: string;

    /**
     * 文章内容
     *
     * @field content
     * @type mediumtext
     */
    content: string;

    /**
     * 向量创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string;

    /**
     * 文章向量 id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 向量修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    modifiedAt: string;

    /**
     * 文章向量
     *
     * @field vector
     * @type json
     */
    vector: Custom.TVector;
}

export {
    IArticleVectorsEntity as IEntity,
};
export type IRepository = ORM.IRepository<IArticleVectorsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IArticleVectorsIndexPRIMARY {

    /**
     * 文章向量 id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index uk_article_id
 * @unique
 * @property articleId
 */
export interface IArticleVectorsIndexUkArticleId {

    /**
     * 文章id
     *
     * @field article_id
     * @type bigint unsigned
     * @unique
     */
    articleId: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'articleId' | 'content' | 'createdAt' | 'id' | 'modifiedAt' | 'vector';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'articleId' | 'content' | 'createdAt' | 'id' | 'modifiedAt' | 'vector';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'article_id' | 'content' | 'created_at' | 'id' | 'modified_at' | 'vector';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'articleId' | 'content' | 'createdAt' | 'id' | 'modifiedAt' | 'vector';