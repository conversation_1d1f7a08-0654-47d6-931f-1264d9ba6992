/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './Sops.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface ISopsCreation {

    /**
     * 数据
     *
     * @field content
     * @type json
     * @nullable
     */
    content: Custom.TContent | null | ORM.IRawSQL;

    /**
     * ID
     *
     * @field id
     * @type int unsigned
     * @primary
     * @optional
     * @autoIncremental
     */
    id?: number | ORM.IRawSQL;

    /**
     * key
     *
     * @field key
     * @type varchar(255)
     * @unique
     */
    key: string | ORM.IRawSQL;

    /**
     * 任务列表
     *
     * @field tasks
     * @type json
     * @nullable
     */
    tasks: Custom.TTasks | null | ORM.IRawSQL;

    /**
     * ticketFieldID
     *
     * @field ticket_field_id
     * @type varchar(64)
     */
    ticketFieldId: string | ORM.IRawSQL;

    /**
     * 标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string | ORM.IRawSQL;
}

export {
    ISopsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface ISopsEntity {

    /**
     * 数据
     *
     * @field content
     * @type json
     * @nullable
     */
    content: Custom.TContent | null;

    /**
     * ID
     *
     * @field id
     * @type int unsigned
     * @primary
     * @optional
     * @autoIncremental
     */
    id: number;

    /**
     * key
     *
     * @field key
     * @type varchar(255)
     * @unique
     */
    key: string;

    /**
     * 任务列表
     *
     * @field tasks
     * @type json
     * @nullable
     */
    tasks: Custom.TTasks | null;

    /**
     * ticketFieldID
     *
     * @field ticket_field_id
     * @type varchar(64)
     */
    ticketFieldId: string;

    /**
     * 标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string;
}

export {
    ISopsEntity as IEntity,
};
export type IRepository = ORM.IRepository<ISopsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface ISopsIndexPRIMARY {

    /**
     * ID
     *
     * @field id
     * @type int unsigned
     * @primary
     * @optional
     * @autoIncremental
     */
    id: number;
}

/**
 * @index uk_key
 * @unique
 * @property key
 */
export interface ISopsIndexUkKey {

    /**
     * key
     *
     * @field key
     * @type varchar(255)
     * @unique
     */
    key: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'content' | 'key' | 'tasks' | 'ticketFieldId' | 'title';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'content' | 'id' | 'key' | 'tasks' | 'ticketFieldId' | 'title';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'content' | 'id' | 'key' | 'tasks' | 'ticket_field_id' | 'title';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'content' | 'key' | 'tasks' | 'ticketFieldId' | 'title';