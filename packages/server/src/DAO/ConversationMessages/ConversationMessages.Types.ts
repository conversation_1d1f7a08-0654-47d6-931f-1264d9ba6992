/* eslint-disable */
import * as ORM from '@reolink-fx/orm';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IConversationMessagesCreation {

    /**
     * 会话id
     *
     * @field conversation_id
     * @type bigint unsigned
     */
    conversationId: string | ORM.IRawSQL;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * 加密消息内容
     *
     * @field encrypted_message
     * @type blob
     */
    encryptedMessage: Buffer | ORM.IRawSQL;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 消息内容 用户的信息需脱敏
     *
     * @field message
     * @type text
     */
    message: string | ORM.IRawSQL;

    /**
     * 角色：0-assistant，1-user
     *
     * @field role
     * @type int unsigned
     */
    role: number | ORM.IRawSQL;

    /**
     * 来源
     *
     * @field source
     * @type varchar(255)
     * @nullable
     */
    source: string | null | ORM.IRawSQL;
}

export {
    IConversationMessagesCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IConversationMessagesEntity {

    /**
     * 会话id
     *
     * @field conversation_id
     * @type bigint unsigned
     */
    conversationId: string;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string;

    /**
     * 加密消息内容
     *
     * @field encrypted_message
     * @type blob
     */
    encryptedMessage: Buffer;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 消息内容 用户的信息需脱敏
     *
     * @field message
     * @type text
     */
    message: string;

    /**
     * 角色：0-assistant，1-user
     *
     * @field role
     * @type int unsigned
     */
    role: number;

    /**
     * 来源
     *
     * @field source
     * @type varchar(255)
     * @nullable
     */
    source: string | null;
}

export {
    IConversationMessagesEntity as IEntity,
};
export type IRepository = ORM.IRepository<IConversationMessagesEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IConversationMessagesIndexPRIMARY {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index Idx_conversation_id
 * @property conversationId
 */
export interface IConversationMessagesIndexIdxConversationId {

    /**
     * 会话id
     *
     * @field conversation_id
     * @type bigint unsigned
     */
    conversationId: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'conversationId' | 'createdAt' | 'encryptedMessage' | 'id' | 'message' | 'role' | 'source';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'conversationId' | 'createdAt' | 'encryptedMessage' | 'id' | 'message' | 'role' | 'source';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'conversation_id' | 'created_at' | 'encrypted_message' | 'id' | 'message' | 'role' | 'source';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'conversationId' | 'createdAt' | 'encryptedMessage' | 'id' | 'message' | 'role' | 'source';