/* eslint-disable */
import * as I from './ConversationMessages.Types';
import * as ORM from '@reolink-fx/orm';
const ALL_MUTABLE_PROPS: Record<I.TEditableProperties, () => string> = Object.freeze({
    'conversationId': () => AbstractConversationMessagesEntity.refInsertValue("conversationId"),
    'createdAt': () => AbstractConversationMessagesEntity.refInsertValue("createdAt"),
    'encryptedMessage': () => AbstractConversationMessagesEntity.refInsertValue("encryptedMessage"),
    'id': () => AbstractConversationMessagesEntity.refInsertValue("id"),
    'message': () => AbstractConversationMessagesEntity.refInsertValue("message"),
    'role': () => AbstractConversationMessagesEntity.refInsertValue("role"),
    'source': () => AbstractConversationMessagesEntity.refInsertValue("source"),
});

export abstract class AbstractConversationMessagesEntity {

    /**
     * 该实体类是否对应一个模版表。
     */
    public static readonly isTemplate: boolean = false;


    /**
     * 在当前 DAO Entity 中，从属性到数据库字段名称的映射表。
     */
    public static readonly propertyToFieldMapping: Readonly<Record<I.TProperties, I.TFields>> = Object.freeze({
        'conversationId': 'conversation_id',
        'createdAt': 'created_at',
        'encryptedMessage': 'encrypted_message',
        'id': 'id',
        'message': 'message',
        'role': 'role',
        'source': 'source',
    });

    /**
     * 将一个标识符转换为可以安全引用的形式。
     */
    public static readonly quoteIdentifier = (identityName: string) => `\`${identityName}\``;

    /**
     * 在当前 DAO Entity 中，从数据库字段名称到属性的映射表。
     */
    public static readonly fieldToPropertyMapping: Readonly<Record<I.TFields, I.TProperties>> = Object.freeze({
        'conversation_id': 'conversationId',
        'created_at': 'createdAt',
        'encrypted_message': 'encryptedMessage',
        'id': 'id',
        'message': 'message',
        'role': 'role',
        'source': 'source',
    });

    /**
     * 插入一行数据到数据库，成功时返回 true。
     *
     * > 当由于唯一索引冲突导致失败时，如果 `ignoreOnDup === true`，那么返回 false，否则抛出异常。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param ignoreOnDup          是否在唯一索引冲突时放弃插入操作（不抛出异常）
     */
    public static async insert(repo: I.IRepository, newRecord: I.ICreation, ignoreOnDup: boolean = false): Promise<boolean> {

        let req = repo.createQuery().insert().values(newRecord);
        const [sql, params] = (ignoreOnDup ? req.orIgnore() : req).getQueryAndParameters();
        return !!(await repo.execute(sql, params))?.affectedRows;
    }

    /**
     * 批量插入数据到数据库。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param ignoreOnDup          是否在唯一索引冲突时放弃插入操作（不抛出异常）
     */
    public static async bulkInsert(repo: I.IRepository, newRecord: Array<I.ICreation>, ignoreOnDup: boolean = false): Promise<void> {

        if (!Array.isArray(newRecord) || !newRecord.length) {
            return;
        }
        let req = repo.createQuery().insert().values(newRecord);
        const [sql, params] = (ignoreOnDup ? req.orIgnore() : req).getQueryAndParameters();
        await repo.execute(sql, params);
    }

    /**
     * 插入一行数据到数据库，并获取插入的行数据。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     */
    public static async insertAndGet(repo: I.IRepository, newRecord: I.ICreation): Promise<I.IEntity> {

        let req = repo.createQuery().insert().values(newRecord);
        await repo.execute(...req.getQueryAndParameters());
        return (await repo.findOne({ where: {
            'id': newRecord.id as any,
        } }))!;
    }

    /**
     * 插入一行数据到数据库，并在发生唯一索引冲突时更新特定字段。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param set                  发生唯一索引冲突时要更新的字段及对应数据
     */
    public static async upsert(repo: I.IRepository, newRecord: I.ICreation | Array<I.ICreation>, set: ORM.QueryDeepPartialEntity<Pick<I.IEntity, I.TEditableProperties>> | "all" | Array<I.TEditableProperties>): Promise<ORM.IExecutionResult> {

        if (Array.isArray(newRecord) && !newRecord.length) {
            return { affectedRows: 0, lastInsertId: 0 };
        }
        if (Array.isArray(set)) {
            set = set.reduce<ORM.QueryDeepPartialEntity<Pick<I.IEntity, I.TEditableProperties>>>((p, q) => ((p[q] = ALL_MUTABLE_PROPS[q]), p), {});
        }
        else if (set === "all") {
            set = ALL_MUTABLE_PROPS
        }
        let [iSQL, iParams] = repo.createQuery().insert().values(newRecord).getQueryAndParameters();
        const [uSQL, uParams] = repo.createQuery().update().set(set).getQueryAndParameters();
        iSQL += ' ON DUPLICATE KEY UPDATE ' + uSQL.slice(uSQL.indexOf('SET ') + 4);
        return repo.execute(iSQL, [...iParams, ...uParams]);
    }

    /**
     * 插入一行数据到数据库，并在发生唯一索引冲突时更新特定字段。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param set                  发生唯一索引冲突时要更新的字段及对应数据
     * @deprecated 使用 `upsert` 代替
     */
    public static readonly insertOrUpdateOnDup = this.upsert;

    /**
     * 生成一个解除了 `this` 绑定的 `refInsertValue` 工具函数。
     *
     * @deprecated 可直接引用 `refInsertValue` 属性。
     */
    public static createInsertValueRefer(): (p: I.TProperties) => string {
        return this.refInsertValue;
    }

    /**
     * 在 upsert 方法中使用，可以获取每一行插入数据中某个字段的值引用。
     *
     * > 如 `refInsertValue('qty')` 将变成 `VALUES([qty])`。 
     *
     * @param p      被引用的属性名称（不是数据库字段名称）
     */
    public static readonly refInsertValue = (p: I.TProperties) => `VALUES(${this.quoteIdentifier(this.propertyToFieldMapping[p])})`;

    /**
     * 生成一个解除了 `this` 绑定的 `refRowValue` 工具函数。
     *
     * @deprecated 可直接引用 `refRowValue` 属性。
     */
    public static createRowValueRefer(): (p: I.TProperties) => string {
        return this.refRowValue;
    }

    /**
     * 在 upsert/update 等方法中使用，可以获取被更新的每一行数据中某个字段的值引用。
     *
     * > 如 `refRowValue('qty')` 将变成 `[qty]`。 
     *
     * @param p      被引用的属性名称（不是数据库字段名称）
     */
    public static readonly refRowValue = (p: I.TProperties) => this.quoteIdentifier(this.propertyToFieldMapping[p])
}

@ORM.Index('Idx_conversation_id', ['conversationId'])
@ORM.Entity('conversation_messages')
export class ConversationMessagesEntity extends AbstractConversationMessagesEntity implements I.IEntity {

    /**
     * 会话id
     *
     * @field conversation_id
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'conversation_id',
        'unsigned': true,
    })
    public conversationId!: string;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'created_at',
        'unsigned': true,
    })
    public createdAt!: string;

    /**
     * 加密消息内容
     *
     * @field encrypted_message
     * @type blob
     */
    @ORM.Column('blob', {
        'name': 'encrypted_message',
    })
    public encryptedMessage!: Buffer;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    @ORM.PrimaryColumn('bigint', {
        'name': 'id',
        'unsigned': true,
    })
    public id!: string;

    /**
     * 消息内容 用户的信息需脱敏
     *
     * @field message
     * @type text
     */
    @ORM.Column('text', {
        'name': 'message',
    })
    public message!: string;

    /**
     * 角色：0-assistant，1-user
     *
     * @field role
     * @type int unsigned
     */
    @ORM.Column('int', {
        'name': 'role',
        'unsigned': true,
    })
    public role!: number;

    /**
     * 来源
     *
     * @field source
     * @type varchar(255)
     * @nullable
     */
    @ORM.Column('varchar', {
        'name': 'source',
        'nullable': true,
        'length': 255,
    })
    public source!: string | null;
}

export {
    ConversationMessagesEntity as Entity,
};
