/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './SopsV2.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface ISopsV2Creation {

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * 创建人
     *
     * @field created_by
     * @type bigint
     */
    createdBy: string | ORM.IRawSQL;

    /**
     * 意图描述
     *
     * @field description
     * @type varchar(1000)
     */
    description: string | ORM.IRawSQL;

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 意图
     *
     * @field intent
     * @type varchar(255)
     * @unique
     */
    intent: string | ORM.IRawSQL;

    /**
     * 更新时间
     *
     * @field modified_at
     * @type bigint
     */
    modifiedAt: string | ORM.IRawSQL;

    /**
     * 更新人
     *
     * @field modified_by
     * @type bigint
     */
    modifiedBy: string | ORM.IRawSQL;

    /**
     * 场景id
     *
     * @field scene_id
     * @type bigint unsigned
     */
    sceneId: string | ORM.IRawSQL;

    /**
     * 解决方案列表
     *
     * @field solutions
     * @type json
     */
    solutions: Custom.TSolutions | ORM.IRawSQL;

    /**
     * 标记分类，如产品分类
     *
     * @field tags
     * @type json
     */
    tags: Custom.TTags | ORM.IRawSQL;
}

export {
    ISopsV2Creation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface ISopsV2Entity {

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint
     */
    createdAt: string;

    /**
     * 创建人
     *
     * @field created_by
     * @type bigint
     */
    createdBy: string;

    /**
     * 意图描述
     *
     * @field description
     * @type varchar(1000)
     */
    description: string;

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 意图
     *
     * @field intent
     * @type varchar(255)
     * @unique
     */
    intent: string;

    /**
     * 更新时间
     *
     * @field modified_at
     * @type bigint
     */
    modifiedAt: string;

    /**
     * 更新人
     *
     * @field modified_by
     * @type bigint
     */
    modifiedBy: string;

    /**
     * 场景id
     *
     * @field scene_id
     * @type bigint unsigned
     */
    sceneId: string;

    /**
     * 解决方案列表
     *
     * @field solutions
     * @type json
     */
    solutions: Custom.TSolutions;

    /**
     * 标记分类，如产品分类
     *
     * @field tags
     * @type json
     */
    tags: Custom.TTags;
}

export {
    ISopsV2Entity as IEntity,
};
export type IRepository = ORM.IRepository<ISopsV2Entity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface ISopsV2IndexPRIMARY {

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index udx_intent
 * @unique
 * @property intent
 */
export interface ISopsV2IndexUdxIntent {

    /**
     * 意图
     *
     * @field intent
     * @type varchar(255)
     * @unique
     */
    intent: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'createdAt' | 'createdBy' | 'description' | 'id' | 'intent' | 'modifiedAt' | 'modifiedBy' | 'sceneId' | 'solutions' | 'tags';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'createdAt' | 'createdBy' | 'description' | 'id' | 'intent' | 'modifiedAt' | 'modifiedBy' | 'sceneId' | 'solutions' | 'tags';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'created_at' | 'created_by' | 'description' | 'id' | 'intent' | 'modified_at' | 'modified_by' | 'scene_id' | 'solutions' | 'tags';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'createdAt' | 'createdBy' | 'description' | 'id' | 'intent' | 'modifiedAt' | 'modifiedBy' | 'sceneId' | 'solutions' | 'tags';