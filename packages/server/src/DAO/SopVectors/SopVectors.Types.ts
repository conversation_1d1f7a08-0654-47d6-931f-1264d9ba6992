/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './SopVectors.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface ISopVectorsCreation {

    /**
     * ID
     *
     * @field id
     * @type int unsigned
     * @primary
     * @optional
     * @autoIncremental
     */
    id?: number | ORM.IRawSQL;

    /**
     * SOP ID
     *
     * @field sop_id
     * @type int unsigned
     */
    sopId: number | ORM.IRawSQL;

    /**
     * 向量
     *
     * @field vector
     * @type json
     */
    vector: Custom.TVector | ORM.IRawSQL;
}

export {
    ISopVectorsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface ISopVectorsEntity {

    /**
     * ID
     *
     * @field id
     * @type int unsigned
     * @primary
     * @optional
     * @autoIncremental
     */
    id: number;

    /**
     * SOP ID
     *
     * @field sop_id
     * @type int unsigned
     */
    sopId: number;

    /**
     * 向量
     *
     * @field vector
     * @type json
     */
    vector: Custom.TVector;
}

export {
    ISopVectorsEntity as IEntity,
};
export type IRepository = ORM.IRepository<ISopVectorsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface ISopVectorsIndexPRIMARY {

    /**
     * ID
     *
     * @field id
     * @type int unsigned
     * @primary
     * @optional
     * @autoIncremental
     */
    id: number;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'sopId' | 'vector';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'id' | 'sopId' | 'vector';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'id' | 'sop_id' | 'vector';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'sopId' | 'vector';