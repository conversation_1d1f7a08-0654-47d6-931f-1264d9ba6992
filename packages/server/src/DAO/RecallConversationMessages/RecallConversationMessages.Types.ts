/* eslint-disable */
import * as ORM from '@reolink-fx/orm';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IRecallConversationMessagesCreation {

    /**
     * 检测消息内容
     *
     * @field content
     * @type text
     */
    content: string | ORM.IRawSQL;

    /**
     * 会话id
     *
     * @field conversation_id
     * @type bigint unsigned
     */
    conversationId: string | ORM.IRawSQL;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 信息id
     *
     * @field message_id
     * @type bigint unsigned
     */
    messageId: string | ORM.IRawSQL;

    /**
     * 撤回原因
     *
     * @field reason
     * @type varchar(255)
     */
    reason: string | ORM.IRawSQL;
}

export {
    IRecallConversationMessagesCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IRecallConversationMessagesEntity {

    /**
     * 检测消息内容
     *
     * @field content
     * @type text
     */
    content: string;

    /**
     * 会话id
     *
     * @field conversation_id
     * @type bigint unsigned
     */
    conversationId: string;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 信息id
     *
     * @field message_id
     * @type bigint unsigned
     */
    messageId: string;

    /**
     * 撤回原因
     *
     * @field reason
     * @type varchar(255)
     */
    reason: string;
}

export {
    IRecallConversationMessagesEntity as IEntity,
};
export type IRepository = ORM.IRepository<IRecallConversationMessagesEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IRecallConversationMessagesIndexPRIMARY {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'content' | 'conversationId' | 'createdAt' | 'id' | 'messageId' | 'reason';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'content' | 'conversationId' | 'createdAt' | 'id' | 'messageId' | 'reason';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'content' | 'conversation_id' | 'created_at' | 'id' | 'message_id' | 'reason';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'content' | 'conversationId' | 'createdAt' | 'id' | 'messageId' | 'reason';