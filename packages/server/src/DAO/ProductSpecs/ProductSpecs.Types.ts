/* eslint-disable */
import * as ORM from '@reolink-fx/orm';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IProductSpecsCreation {

    /**
     * 产品规格文本
     *
     * @field content
     * @type text
     */
    content: string | ORM.IRawSQL;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 产品key
     *
     * @field product
     * @type varchar(255)
     * @unique
     */
    product: string | ORM.IRawSQL;
}

export {
    IProductSpecsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IProductSpecsEntity {

    /**
     * 产品规格文本
     *
     * @field content
     * @type text
     */
    content: string;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 产品key
     *
     * @field product
     * @type varchar(255)
     * @unique
     */
    product: string;
}

export {
    IProductSpecsEntity as IEntity,
};
export type IRepository = ORM.IRepository<IProductSpecsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IProductSpecsIndexPRIMARY {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index uk_product
 * @unique
 * @property product
 */
export interface IProductSpecsIndexUkProduct {

    /**
     * 产品key
     *
     * @field product
     * @type varchar(255)
     * @unique
     */
    product: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'content' | 'id' | 'product';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'content' | 'id' | 'product';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'content' | 'id' | 'product';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'content' | 'id' | 'product';