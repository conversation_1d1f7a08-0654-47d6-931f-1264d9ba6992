/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './EncryptionRecords.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IEncryptionRecordsCreation {

    /**
     * 加密数据ID
     *
     * @field cipher_data_id
     * @type bigint unsigned
     * @unique
     */
    cipherDataId: string | ORM.IRawSQL;

    /**
     * 存储加密套件信息
     *
     * @field cipher_suite
     * @type json
     */
    cipherSuite: Custom.TCipherSuite | ORM.IRawSQL;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * 主键 ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     * @optional
     * @autoIncremental
     */
    id?: string | ORM.IRawSQL;

    /**
     * 更新时间
     *
     * @field modified_at
     * @type bigint
     */
    modifiedAt: string | ORM.IRawSQL;
}

export {
    IEncryptionRecordsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IEncryptionRecordsEntity {

    /**
     * 加密数据ID
     *
     * @field cipher_data_id
     * @type bigint unsigned
     * @unique
     */
    cipherDataId: string;

    /**
     * 存储加密套件信息
     *
     * @field cipher_suite
     * @type json
     */
    cipherSuite: Custom.TCipherSuite;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint
     */
    createdAt: string;

    /**
     * 主键 ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     * @optional
     * @autoIncremental
     */
    id: string;

    /**
     * 更新时间
     *
     * @field modified_at
     * @type bigint
     */
    modifiedAt: string;
}

export {
    IEncryptionRecordsEntity as IEntity,
};
export type IRepository = ORM.IRepository<IEncryptionRecordsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IEncryptionRecordsIndexPRIMARY {

    /**
     * 主键 ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     * @optional
     * @autoIncremental
     */
    id: string;
}

/**
 * @index cipher_data_id_unique
 * @unique
 * @property cipherDataId
 */
export interface IEncryptionRecordsIndexCipherDataIdUnique {

    /**
     * 加密数据ID
     *
     * @field cipher_data_id
     * @type bigint unsigned
     * @unique
     */
    cipherDataId: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'cipherDataId' | 'cipherSuite' | 'createdAt' | 'modifiedAt';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'cipherDataId' | 'cipherSuite' | 'createdAt' | 'id' | 'modifiedAt';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'cipher_data_id' | 'cipher_suite' | 'created_at' | 'id' | 'modified_at';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'cipherDataId' | 'cipherSuite' | 'createdAt' | 'modifiedAt';