/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './ConversationFeedback.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IConversationFeedbackCreation {

    /**
     * 额外评论
     *
     * @field additional_comments
     * @type varchar(255)
     * @nullable
     */
    additionalComments: string | null | ORM.IRawSQL;

    /**
     * 会话id
     *
     * @field conversation_id
     * @type bigint unsigned
     * @unique
     */
    conversationId: string | ORM.IRawSQL;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 问题是否得到解决（0-否，1-是）
     *
     * @field issue_resolved
     * @type tinyint unsigned
     */
    issueResolved: number | ORM.IRawSQL;

    /**
     * 未解决问题的原因列表
     *
     * @field problem_unresolved_reasons
     * @type json
     */
    problemUnresolvedReasons: Custom.TProblemUnresolvedReasons | ORM.IRawSQL;

    /**
     * 用户id，未登录为0
     *
     * @field user_id
     * @type bigint unsigned
     */
    userId: string | ORM.IRawSQL;
}

export {
    IConversationFeedbackCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IConversationFeedbackEntity {

    /**
     * 额外评论
     *
     * @field additional_comments
     * @type varchar(255)
     * @nullable
     */
    additionalComments: string | null;

    /**
     * 会话id
     *
     * @field conversation_id
     * @type bigint unsigned
     * @unique
     */
    conversationId: string;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 问题是否得到解决（0-否，1-是）
     *
     * @field issue_resolved
     * @type tinyint unsigned
     */
    issueResolved: number;

    /**
     * 未解决问题的原因列表
     *
     * @field problem_unresolved_reasons
     * @type json
     */
    problemUnresolvedReasons: Custom.TProblemUnresolvedReasons;

    /**
     * 用户id，未登录为0
     *
     * @field user_id
     * @type bigint unsigned
     */
    userId: string;
}

export {
    IConversationFeedbackEntity as IEntity,
};
export type IRepository = ORM.IRepository<IConversationFeedbackEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IConversationFeedbackIndexPRIMARY {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index uk_conversation_id
 * @unique
 * @property conversationId
 */
export interface IConversationFeedbackIndexUkConversationId {

    /**
     * 会话id
     *
     * @field conversation_id
     * @type bigint unsigned
     * @unique
     */
    conversationId: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'additionalComments' | 'conversationId' | 'createdAt' | 'id' | 'issueResolved' | 'problemUnresolvedReasons' | 'userId';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'additionalComments' | 'conversationId' | 'createdAt' | 'id' | 'issueResolved' | 'problemUnresolvedReasons' | 'userId';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'additional_comments' | 'conversation_id' | 'created_at' | 'id' | 'issue_resolved' | 'problem_unresolved_reasons' | 'user_id';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'additionalComments' | 'conversationId' | 'createdAt' | 'id' | 'issueResolved' | 'problemUnresolvedReasons' | 'userId';