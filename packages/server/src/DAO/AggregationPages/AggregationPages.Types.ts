/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './AggregationPages.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IAggregationPagesCreation {

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * 创建人
     *
     * @field created_by
     * @type bigint unsigned
     */
    createdBy: string | ORM.IRawSQL;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    modifiedAt: string | ORM.IRawSQL;

    /**
     * 修改人
     *
     * @field modified_by
     * @type bigint unsigned
     */
    modifiedBy: string | ORM.IRawSQL;

    /**
     * SEO相关信息
     *
     * @field seo_meta
     * @type json
     */
    seoMeta: Custom.TSeoMeta | ORM.IRawSQL;

    /**
     * 聚合页标识符
     *
     * @field slug
     * @type varchar(255)
     * @unique
     */
    slug: string | ORM.IRawSQL;

    /**
     * 文章状态：1-草稿，2-发布
     *
     * @field status
     * @type tinyint
     */
    status: number | ORM.IRawSQL;

    /**
     * 聚合页标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string | ORM.IRawSQL;
}

export {
    IAggregationPagesCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IAggregationPagesEntity {

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string;

    /**
     * 创建人
     *
     * @field created_by
     * @type bigint unsigned
     */
    createdBy: string;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    modifiedAt: string;

    /**
     * 修改人
     *
     * @field modified_by
     * @type bigint unsigned
     */
    modifiedBy: string;

    /**
     * SEO相关信息
     *
     * @field seo_meta
     * @type json
     */
    seoMeta: Custom.TSeoMeta;

    /**
     * 聚合页标识符
     *
     * @field slug
     * @type varchar(255)
     * @unique
     */
    slug: string;

    /**
     * 文章状态：1-草稿，2-发布
     *
     * @field status
     * @type tinyint
     */
    status: number;

    /**
     * 聚合页标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string;
}

export {
    IAggregationPagesEntity as IEntity,
};
export type IRepository = ORM.IRepository<IAggregationPagesEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IAggregationPagesIndexPRIMARY {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index uk_slug
 * @unique
 * @property slug
 */
export interface IAggregationPagesIndexUkSlug {

    /**
     * 聚合页标识符
     *
     * @field slug
     * @type varchar(255)
     * @unique
     */
    slug: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'createdAt' | 'createdBy' | 'id' | 'modifiedAt' | 'modifiedBy' | 'seoMeta' | 'slug' | 'status' | 'title';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'createdAt' | 'createdBy' | 'id' | 'modifiedAt' | 'modifiedBy' | 'seoMeta' | 'slug' | 'status' | 'title';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'created_at' | 'created_by' | 'id' | 'modified_at' | 'modified_by' | 'seo_meta' | 'slug' | 'status' | 'title';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'createdAt' | 'createdBy' | 'id' | 'modifiedAt' | 'modifiedBy' | 'seoMeta' | 'slug' | 'status' | 'title';