/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './Conversations.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IConversationsCreation {

    /**
     * 额外评论
     *
     * @field additional_comments
     * @type varchar(255)
     * @nullable
     */
    additionalComments: string | null | ORM.IRawSQL;

    /**
     * 对此ai对话的评价
     *
     * @field agent_comments
     * @type varchar(1024)
     * @nullable
     */
    agentComments: string | null | ORM.IRawSQL;

    /**
     * agent标记的tag 如：合理 / 不合理
     *
     * @field agent_tags
     * @type json
     */
    agentTags: Custom.TAgentTags | ORM.IRawSQL;

    /**
     * 问题分类 0：其它 1：咨询类 2：排查类 
     *
     * @field category
     * @type int unsigned
     */
    category: number | ORM.IRawSQL;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * 情绪 0：中性 1：积极 2：消极 3：异常消极 
     *
     * @field emotion
     * @type int unsigned
     */
    emotion: number | ORM.IRawSQL;

    /**
     * 反馈创建时间
     *
     * @field feedback_created_at
     * @type bigint unsigned
     * @nullable
     */
    feedbackCreatedAt: string | null | ORM.IRawSQL;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 意图
     *
     * @field intent
     * @type varchar(255)
     */
    intent: string | ORM.IRawSQL;

    /**
     * 是否创建工单
     *
     * @field is_create_ticket
     * @type tinyint(1)
     */
    isCreateTicket: number | ORM.IRawSQL;

    /**
     * 是否为内部测试对话，1是，0否
     *
     * @field is_internal_test
     * @type tinyint(1)
     */
    isInternalTest: number | ORM.IRawSQL;

    /**
     * 是否转人工
     *
     * @field is_pass_to_agent
     * @type tinyint(1)
     */
    isPassToAgent: number | ORM.IRawSQL;

    /**
     * 意图是否错误
     *
     * @field is_wrong_intent
     * @type tinyint(1)
     */
    isWrongIntent: number | ORM.IRawSQL;

    /**
     * 问题是否解决（0-否，1-是）
     *
     * @field issue_resolved
     * @type tinyint unsigned
     * @nullable
     */
    issueResolved: number | null | ORM.IRawSQL;

    /**
     * sop执行的最后一个步骤
     *
     * @field last_step_id
     * @type bigint unsigned
     */
    lastStepId: string | ORM.IRawSQL;

    /**
     * 元数据
     *
     * @field metadata
     * @type json
     */
    metadata: Custom.TMetadata | ORM.IRawSQL;

    /**
     * 修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    modifiedAt: string | ORM.IRawSQL;

    /**
     * 未解决问题的原因列表
     *
     * @field problem_unresolved_reasons
     * @type json
     * @nullable
     */
    problemUnresolvedReasons: Custom.TProblemUnresolvedReasons | null | ORM.IRawSQL;

    /**
     * 已推送的sop步骤
     *
     * @field pushed_steps
     * @type json
     */
    pushedSteps: Custom.TPushedSteps | ORM.IRawSQL;

    /**
     * 最后应用的sopid
     *
     * @field sop_id
     * @type bigint unsigned
     */
    sopId: string | ORM.IRawSQL;

    /**
     * 用户id，未登录为0
     *
     * @field user_id
     * @type bigint unsigned
     */
    userId: string | ORM.IRawSQL;

    /**
     * zd会话id
     *
     * @field zd_conversation_id
     * @type varchar(64)
     */
    zdConversationId: string | ORM.IRawSQL;

    /**
     * zd用户id
     *
     * @field zd_user_id
     * @type varchar(64)
     */
    zdUserId: string | ORM.IRawSQL;
}

export {
    IConversationsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IConversationsEntity {

    /**
     * 额外评论
     *
     * @field additional_comments
     * @type varchar(255)
     * @nullable
     */
    additionalComments: string | null;

    /**
     * 对此ai对话的评价
     *
     * @field agent_comments
     * @type varchar(1024)
     * @nullable
     */
    agentComments: string | null;

    /**
     * agent标记的tag 如：合理 / 不合理
     *
     * @field agent_tags
     * @type json
     */
    agentTags: Custom.TAgentTags;

    /**
     * 问题分类 0：其它 1：咨询类 2：排查类 
     *
     * @field category
     * @type int unsigned
     */
    category: number;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string;

    /**
     * 情绪 0：中性 1：积极 2：消极 3：异常消极 
     *
     * @field emotion
     * @type int unsigned
     */
    emotion: number;

    /**
     * 反馈创建时间
     *
     * @field feedback_created_at
     * @type bigint unsigned
     * @nullable
     */
    feedbackCreatedAt: string | null;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 意图
     *
     * @field intent
     * @type varchar(255)
     */
    intent: string;

    /**
     * 是否创建工单
     *
     * @field is_create_ticket
     * @type tinyint(1)
     */
    isCreateTicket: number;

    /**
     * 是否为内部测试对话，1是，0否
     *
     * @field is_internal_test
     * @type tinyint(1)
     */
    isInternalTest: number;

    /**
     * 是否转人工
     *
     * @field is_pass_to_agent
     * @type tinyint(1)
     */
    isPassToAgent: number;

    /**
     * 意图是否错误
     *
     * @field is_wrong_intent
     * @type tinyint(1)
     */
    isWrongIntent: number;

    /**
     * 问题是否解决（0-否，1-是）
     *
     * @field issue_resolved
     * @type tinyint unsigned
     * @nullable
     */
    issueResolved: number | null;

    /**
     * sop执行的最后一个步骤
     *
     * @field last_step_id
     * @type bigint unsigned
     */
    lastStepId: string;

    /**
     * 元数据
     *
     * @field metadata
     * @type json
     */
    metadata: Custom.TMetadata;

    /**
     * 修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    modifiedAt: string;

    /**
     * 未解决问题的原因列表
     *
     * @field problem_unresolved_reasons
     * @type json
     * @nullable
     */
    problemUnresolvedReasons: Custom.TProblemUnresolvedReasons | null;

    /**
     * 已推送的sop步骤
     *
     * @field pushed_steps
     * @type json
     */
    pushedSteps: Custom.TPushedSteps;

    /**
     * 最后应用的sopid
     *
     * @field sop_id
     * @type bigint unsigned
     */
    sopId: string;

    /**
     * 用户id，未登录为0
     *
     * @field user_id
     * @type bigint unsigned
     */
    userId: string;

    /**
     * zd会话id
     *
     * @field zd_conversation_id
     * @type varchar(64)
     */
    zdConversationId: string;

    /**
     * zd用户id
     *
     * @field zd_user_id
     * @type varchar(64)
     */
    zdUserId: string;
}

export {
    IConversationsEntity as IEntity,
};
export type IRepository = ORM.IRepository<IConversationsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IConversationsIndexPRIMARY {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index Idx_intent
 * @property intent
 */
export interface IConversationsIndexIdxIntent {

    /**
     * 意图
     *
     * @field intent
     * @type varchar(255)
     */
    intent: string;
}

/**
 * @index Idx_sop_id
 * @property sopId
 */
export interface IConversationsIndexIdxSopId {

    /**
     * 最后应用的sopid
     *
     * @field sop_id
     * @type bigint unsigned
     */
    sopId: string;
}

/**
 * @index Idx_zd_cid
 * @property zdConversationId
 */
export interface IConversationsIndexIdxZdCid {

    /**
     * zd会话id
     *
     * @field zd_conversation_id
     * @type varchar(64)
     */
    zdConversationId: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'additionalComments' | 'agentComments' | 'agentTags' | 'category' | 'createdAt' | 'emotion' | 'feedbackCreatedAt' | 'id' | 'intent' | 'isCreateTicket' | 'isInternalTest' | 'isPassToAgent' | 'isWrongIntent' | 'issueResolved' | 'lastStepId' | 'metadata' | 'modifiedAt' | 'problemUnresolvedReasons' | 'pushedSteps' | 'sopId' | 'userId' | 'zdConversationId' | 'zdUserId';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'additionalComments' | 'agentComments' | 'agentTags' | 'category' | 'createdAt' | 'emotion' | 'feedbackCreatedAt' | 'id' | 'intent' | 'isCreateTicket' | 'isInternalTest' | 'isPassToAgent' | 'isWrongIntent' | 'issueResolved' | 'lastStepId' | 'metadata' | 'modifiedAt' | 'problemUnresolvedReasons' | 'pushedSteps' | 'sopId' | 'userId' | 'zdConversationId' | 'zdUserId';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'additional_comments' | 'agent_comments' | 'agent_tags' | 'category' | 'created_at' | 'emotion' | 'feedback_created_at' | 'id' | 'intent' | 'is_create_ticket' | 'is_internal_test' | 'is_pass_to_agent' | 'is_wrong_intent' | 'issue_resolved' | 'last_step_id' | 'metadata' | 'modified_at' | 'problem_unresolved_reasons' | 'pushed_steps' | 'sop_id' | 'user_id' | 'zd_conversation_id' | 'zd_user_id';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'additionalComments' | 'agentComments' | 'agentTags' | 'category' | 'createdAt' | 'emotion' | 'feedbackCreatedAt' | 'id' | 'intent' | 'isCreateTicket' | 'isInternalTest' | 'isPassToAgent' | 'isWrongIntent' | 'issueResolved' | 'lastStepId' | 'metadata' | 'modifiedAt' | 'problemUnresolvedReasons' | 'pushedSteps' | 'sopId' | 'userId' | 'zdConversationId' | 'zdUserId';