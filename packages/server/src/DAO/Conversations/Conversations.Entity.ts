/* eslint-disable */
import * as I from './Conversations.Types';
import * as Custom from './Conversations.Custom';
import * as ORM from '@reolink-fx/orm';
const ALL_MUTABLE_PROPS: Record<I.TEditableProperties, () => string> = Object.freeze({
    'additionalComments': () => AbstractConversationsEntity.refInsertValue("additionalComments"),
    'agentComments': () => AbstractConversationsEntity.refInsertValue("agentComments"),
    'agentTags': () => AbstractConversationsEntity.refInsertValue("agentTags"),
    'category': () => AbstractConversationsEntity.refInsertValue("category"),
    'createdAt': () => AbstractConversationsEntity.refInsertValue("createdAt"),
    'emotion': () => AbstractConversationsEntity.refInsertValue("emotion"),
    'feedbackCreatedAt': () => AbstractConversationsEntity.refInsertValue("feedbackCreatedAt"),
    'id': () => AbstractConversationsEntity.refInsertValue("id"),
    'intent': () => AbstractConversationsEntity.refInsertValue("intent"),
    'isCreateTicket': () => AbstractConversationsEntity.refInsertValue("isCreateTicket"),
    'isInternalTest': () => AbstractConversationsEntity.refInsertValue("isInternalTest"),
    'isPassToAgent': () => AbstractConversationsEntity.refInsertValue("isPassToAgent"),
    'isWrongIntent': () => AbstractConversationsEntity.refInsertValue("isWrongIntent"),
    'issueResolved': () => AbstractConversationsEntity.refInsertValue("issueResolved"),
    'lastStepId': () => AbstractConversationsEntity.refInsertValue("lastStepId"),
    'metadata': () => AbstractConversationsEntity.refInsertValue("metadata"),
    'modifiedAt': () => AbstractConversationsEntity.refInsertValue("modifiedAt"),
    'problemUnresolvedReasons': () => AbstractConversationsEntity.refInsertValue("problemUnresolvedReasons"),
    'pushedSteps': () => AbstractConversationsEntity.refInsertValue("pushedSteps"),
    'sopId': () => AbstractConversationsEntity.refInsertValue("sopId"),
    'userId': () => AbstractConversationsEntity.refInsertValue("userId"),
    'zdConversationId': () => AbstractConversationsEntity.refInsertValue("zdConversationId"),
    'zdUserId': () => AbstractConversationsEntity.refInsertValue("zdUserId"),
});

export abstract class AbstractConversationsEntity {

    /**
     * 该实体类是否对应一个模版表。
     */
    public static readonly isTemplate: boolean = false;


    /**
     * 在当前 DAO Entity 中，从属性到数据库字段名称的映射表。
     */
    public static readonly propertyToFieldMapping: Readonly<Record<I.TProperties, I.TFields>> = Object.freeze({
        'additionalComments': 'additional_comments',
        'agentComments': 'agent_comments',
        'agentTags': 'agent_tags',
        'category': 'category',
        'createdAt': 'created_at',
        'emotion': 'emotion',
        'feedbackCreatedAt': 'feedback_created_at',
        'id': 'id',
        'intent': 'intent',
        'isCreateTicket': 'is_create_ticket',
        'isInternalTest': 'is_internal_test',
        'isPassToAgent': 'is_pass_to_agent',
        'isWrongIntent': 'is_wrong_intent',
        'issueResolved': 'issue_resolved',
        'lastStepId': 'last_step_id',
        'metadata': 'metadata',
        'modifiedAt': 'modified_at',
        'problemUnresolvedReasons': 'problem_unresolved_reasons',
        'pushedSteps': 'pushed_steps',
        'sopId': 'sop_id',
        'userId': 'user_id',
        'zdConversationId': 'zd_conversation_id',
        'zdUserId': 'zd_user_id',
    });

    /**
     * 将一个标识符转换为可以安全引用的形式。
     */
    public static readonly quoteIdentifier = (identityName: string) => `\`${identityName}\``;

    /**
     * 在当前 DAO Entity 中，从数据库字段名称到属性的映射表。
     */
    public static readonly fieldToPropertyMapping: Readonly<Record<I.TFields, I.TProperties>> = Object.freeze({
        'additional_comments': 'additionalComments',
        'agent_comments': 'agentComments',
        'agent_tags': 'agentTags',
        'category': 'category',
        'created_at': 'createdAt',
        'emotion': 'emotion',
        'feedback_created_at': 'feedbackCreatedAt',
        'id': 'id',
        'intent': 'intent',
        'is_create_ticket': 'isCreateTicket',
        'is_internal_test': 'isInternalTest',
        'is_pass_to_agent': 'isPassToAgent',
        'is_wrong_intent': 'isWrongIntent',
        'issue_resolved': 'issueResolved',
        'last_step_id': 'lastStepId',
        'metadata': 'metadata',
        'modified_at': 'modifiedAt',
        'problem_unresolved_reasons': 'problemUnresolvedReasons',
        'pushed_steps': 'pushedSteps',
        'sop_id': 'sopId',
        'user_id': 'userId',
        'zd_conversation_id': 'zdConversationId',
        'zd_user_id': 'zdUserId',
    });

    /**
     * 插入一行数据到数据库，成功时返回 true。
     *
     * > 当由于唯一索引冲突导致失败时，如果 `ignoreOnDup === true`，那么返回 false，否则抛出异常。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param ignoreOnDup          是否在唯一索引冲突时放弃插入操作（不抛出异常）
     */
    public static async insert(repo: I.IRepository, newRecord: I.ICreation, ignoreOnDup: boolean = false): Promise<boolean> {

        let req = repo.createQuery().insert().values(newRecord);
        const [sql, params] = (ignoreOnDup ? req.orIgnore() : req).getQueryAndParameters();
        return !!(await repo.execute(sql, params))?.affectedRows;
    }

    /**
     * 批量插入数据到数据库。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param ignoreOnDup          是否在唯一索引冲突时放弃插入操作（不抛出异常）
     */
    public static async bulkInsert(repo: I.IRepository, newRecord: Array<I.ICreation>, ignoreOnDup: boolean = false): Promise<void> {

        if (!Array.isArray(newRecord) || !newRecord.length) {
            return;
        }
        let req = repo.createQuery().insert().values(newRecord);
        const [sql, params] = (ignoreOnDup ? req.orIgnore() : req).getQueryAndParameters();
        await repo.execute(sql, params);
    }

    /**
     * 插入一行数据到数据库，并获取插入的行数据。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     */
    public static async insertAndGet(repo: I.IRepository, newRecord: I.ICreation): Promise<I.IEntity> {

        let req = repo.createQuery().insert().values(newRecord);
        await repo.execute(...req.getQueryAndParameters());
        return (await repo.findOne({ where: {
            'id': newRecord.id as any,
        } }))!;
    }

    /**
     * 插入一行数据到数据库，并在发生唯一索引冲突时更新特定字段。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param set                  发生唯一索引冲突时要更新的字段及对应数据
     */
    public static async upsert(repo: I.IRepository, newRecord: I.ICreation | Array<I.ICreation>, set: ORM.QueryDeepPartialEntity<Pick<I.IEntity, I.TEditableProperties>> | "all" | Array<I.TEditableProperties>): Promise<ORM.IExecutionResult> {

        if (Array.isArray(newRecord) && !newRecord.length) {
            return { affectedRows: 0, lastInsertId: 0 };
        }
        if (Array.isArray(set)) {
            set = set.reduce<ORM.QueryDeepPartialEntity<Pick<I.IEntity, I.TEditableProperties>>>((p, q) => ((p[q] = ALL_MUTABLE_PROPS[q]), p), {});
        }
        else if (set === "all") {
            set = ALL_MUTABLE_PROPS
        }
        let [iSQL, iParams] = repo.createQuery().insert().values(newRecord).getQueryAndParameters();
        const [uSQL, uParams] = repo.createQuery().update().set(set).getQueryAndParameters();
        iSQL += ' ON DUPLICATE KEY UPDATE ' + uSQL.slice(uSQL.indexOf('SET ') + 4);
        return repo.execute(iSQL, [...iParams, ...uParams]);
    }

    /**
     * 插入一行数据到数据库，并在发生唯一索引冲突时更新特定字段。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param set                  发生唯一索引冲突时要更新的字段及对应数据
     * @deprecated 使用 `upsert` 代替
     */
    public static readonly insertOrUpdateOnDup = this.upsert;

    /**
     * 生成一个解除了 `this` 绑定的 `refInsertValue` 工具函数。
     *
     * @deprecated 可直接引用 `refInsertValue` 属性。
     */
    public static createInsertValueRefer(): (p: I.TProperties) => string {
        return this.refInsertValue;
    }

    /**
     * 在 upsert 方法中使用，可以获取每一行插入数据中某个字段的值引用。
     *
     * > 如 `refInsertValue('qty')` 将变成 `VALUES([qty])`。 
     *
     * @param p      被引用的属性名称（不是数据库字段名称）
     */
    public static readonly refInsertValue = (p: I.TProperties) => `VALUES(${this.quoteIdentifier(this.propertyToFieldMapping[p])})`;

    /**
     * 生成一个解除了 `this` 绑定的 `refRowValue` 工具函数。
     *
     * @deprecated 可直接引用 `refRowValue` 属性。
     */
    public static createRowValueRefer(): (p: I.TProperties) => string {
        return this.refRowValue;
    }

    /**
     * 在 upsert/update 等方法中使用，可以获取被更新的每一行数据中某个字段的值引用。
     *
     * > 如 `refRowValue('qty')` 将变成 `[qty]`。 
     *
     * @param p      被引用的属性名称（不是数据库字段名称）
     */
    public static readonly refRowValue = (p: I.TProperties) => this.quoteIdentifier(this.propertyToFieldMapping[p])
}

@ORM.Index('Idx_intent', ['intent'])
@ORM.Index('Idx_sop_id', ['sopId'])
@ORM.Index('Idx_zd_cid', ['zdConversationId'])
@ORM.Entity('conversations')
export class ConversationsEntity extends AbstractConversationsEntity implements I.IEntity {

    /**
     * 额外评论
     *
     * @field additional_comments
     * @type varchar(255)
     * @nullable
     */
    @ORM.Column('varchar', {
        'name': 'additional_comments',
        'nullable': true,
        'length': 255,
    })
    public additionalComments!: string | null;

    /**
     * 对此ai对话的评价
     *
     * @field agent_comments
     * @type varchar(1024)
     * @nullable
     */
    @ORM.Column('varchar', {
        'name': 'agent_comments',
        'nullable': true,
        'length': 1024,
    })
    public agentComments!: string | null;

    /**
     * agent标记的tag 如：合理 / 不合理
     *
     * @field agent_tags
     * @type json
     */
    @ORM.Column('json', {
        'name': 'agent_tags',
    })
    public agentTags!: Custom.TAgentTags;

    /**
     * 问题分类 0：其它 1：咨询类 2：排查类 
     *
     * @field category
     * @type int unsigned
     */
    @ORM.Column('int', {
        'name': 'category',
        'unsigned': true,
    })
    public category!: number;

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'created_at',
        'unsigned': true,
    })
    public createdAt!: string;

    /**
     * 情绪 0：中性 1：积极 2：消极 3：异常消极 
     *
     * @field emotion
     * @type int unsigned
     */
    @ORM.Column('int', {
        'name': 'emotion',
        'unsigned': true,
    })
    public emotion!: number;

    /**
     * 反馈创建时间
     *
     * @field feedback_created_at
     * @type bigint unsigned
     * @nullable
     */
    @ORM.Column('bigint', {
        'name': 'feedback_created_at',
        'unsigned': true,
        'nullable': true,
    })
    public feedbackCreatedAt!: string | null;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    @ORM.PrimaryColumn('bigint', {
        'name': 'id',
        'unsigned': true,
    })
    public id!: string;

    /**
     * 意图
     *
     * @field intent
     * @type varchar(255)
     */
    @ORM.Column('varchar', {
        'name': 'intent',
        'length': 255,
    })
    public intent!: string;

    /**
     * 是否创建工单
     *
     * @field is_create_ticket
     * @type tinyint(1)
     */
    @ORM.Column('tinyint', {
        'name': 'is_create_ticket',
    })
    public isCreateTicket!: number;

    /**
     * 是否为内部测试对话，1是，0否
     *
     * @field is_internal_test
     * @type tinyint(1)
     */
    @ORM.Column('tinyint', {
        'name': 'is_internal_test',
    })
    public isInternalTest!: number;

    /**
     * 是否转人工
     *
     * @field is_pass_to_agent
     * @type tinyint(1)
     */
    @ORM.Column('tinyint', {
        'name': 'is_pass_to_agent',
    })
    public isPassToAgent!: number;

    /**
     * 意图是否错误
     *
     * @field is_wrong_intent
     * @type tinyint(1)
     */
    @ORM.Column('tinyint', {
        'name': 'is_wrong_intent',
    })
    public isWrongIntent!: number;

    /**
     * 问题是否解决（0-否，1-是）
     *
     * @field issue_resolved
     * @type tinyint unsigned
     * @nullable
     */
    @ORM.Column('tinyint', {
        'name': 'issue_resolved',
        'unsigned': true,
        'nullable': true,
    })
    public issueResolved!: number | null;

    /**
     * sop执行的最后一个步骤
     *
     * @field last_step_id
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'last_step_id',
        'unsigned': true,
    })
    public lastStepId!: string;

    /**
     * 元数据
     *
     * @field metadata
     * @type json
     */
    @ORM.Column('json', {
        'name': 'metadata',
    })
    public metadata!: Custom.TMetadata;

    /**
     * 修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'modified_at',
        'unsigned': true,
    })
    public modifiedAt!: string;

    /**
     * 未解决问题的原因列表
     *
     * @field problem_unresolved_reasons
     * @type json
     * @nullable
     */
    @ORM.Column('json', {
        'name': 'problem_unresolved_reasons',
        'nullable': true,
    })
    public problemUnresolvedReasons!: Custom.TProblemUnresolvedReasons | null;

    /**
     * 已推送的sop步骤
     *
     * @field pushed_steps
     * @type json
     */
    @ORM.Column('json', {
        'name': 'pushed_steps',
    })
    public pushedSteps!: Custom.TPushedSteps;

    /**
     * 最后应用的sopid
     *
     * @field sop_id
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'sop_id',
        'unsigned': true,
    })
    public sopId!: string;

    /**
     * 用户id，未登录为0
     *
     * @field user_id
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'user_id',
        'unsigned': true,
    })
    public userId!: string;

    /**
     * zd会话id
     *
     * @field zd_conversation_id
     * @type varchar(64)
     */
    @ORM.Column('varchar', {
        'name': 'zd_conversation_id',
        'length': 64,
    })
    public zdConversationId!: string;

    /**
     * zd用户id
     *
     * @field zd_user_id
     * @type varchar(64)
     */
    @ORM.Column('varchar', {
        'name': 'zd_user_id',
        'length': 64,
    })
    public zdUserId!: string;
}

export {
    ConversationsEntity as Entity,
};
