/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './AggregationPagesTabs.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IAggregationPagesTabsCreation {

    /**
     * 聚合页切页锚点
     *
     * @field anchor
     * @type varchar(255)
     * @unique
     */
    anchor: string | ORM.IRawSQL;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 聚合页栏目id
     *
     * @field item_id
     * @type bigint unsigned
     */
    itemId: string | ORM.IRawSQL;

    /**
     * 切页相关信息
     *
     * @field meta
     * @type json
     */
    meta: Custom.TMeta | ORM.IRawSQL;

    /**
     * 聚合页id
     *
     * @field page_id
     * @type bigint unsigned
     */
    pageId: string | ORM.IRawSQL;

    /**
     * 资源id
     *
     * @field resource_id
     * @type varchar(255)
     */
    resourceId: string | ORM.IRawSQL;

    /**
     * 聚合页切页标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string | ORM.IRawSQL;
}

export {
    IAggregationPagesTabsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IAggregationPagesTabsEntity {

    /**
     * 聚合页切页锚点
     *
     * @field anchor
     * @type varchar(255)
     * @unique
     */
    anchor: string;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 聚合页栏目id
     *
     * @field item_id
     * @type bigint unsigned
     */
    itemId: string;

    /**
     * 切页相关信息
     *
     * @field meta
     * @type json
     */
    meta: Custom.TMeta;

    /**
     * 聚合页id
     *
     * @field page_id
     * @type bigint unsigned
     */
    pageId: string;

    /**
     * 资源id
     *
     * @field resource_id
     * @type varchar(255)
     */
    resourceId: string;

    /**
     * 聚合页切页标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string;
}

export {
    IAggregationPagesTabsEntity as IEntity,
};
export type IRepository = ORM.IRepository<IAggregationPagesTabsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IAggregationPagesTabsIndexPRIMARY {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index uk_anchor
 * @unique
 * @property anchor
 */
export interface IAggregationPagesTabsIndexUkAnchor {

    /**
     * 聚合页切页锚点
     *
     * @field anchor
     * @type varchar(255)
     * @unique
     */
    anchor: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'anchor' | 'id' | 'itemId' | 'meta' | 'pageId' | 'resourceId' | 'title';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'anchor' | 'id' | 'itemId' | 'meta' | 'pageId' | 'resourceId' | 'title';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'anchor' | 'id' | 'item_id' | 'meta' | 'page_id' | 'resource_id' | 'title';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'anchor' | 'id' | 'itemId' | 'meta' | 'pageId' | 'resourceId' | 'title';