/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './Articles.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IArticlesCreation {

    /**
     * 文章内容 HTML
     *
     * @field body
     * @type mediumtext
     */
    body: string | ORM.IRawSQL;

    /**
     * 是否禁用评论功能
     *
     * @field comments_disabled
     * @type tinyint
     */
    commentsDisabled: number | ORM.IRawSQL;

    /**
     * 文章附带的内容标签 id 列表
     *
     * @field content_tag_ids
     * @type json
     */
    contentTagIds: Custom.TContentTagIds | ORM.IRawSQL;

    /**
     * 文章创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * 文章是否已被删除
     *
     * @field deleted
     * @type tinyint
     */
    deleted: number | ORM.IRawSQL;

    /**
     * 是否为草稿
     *
     * @field draft
     * @type tinyint
     */
    draft: number | ORM.IRawSQL;

    /**
     * 最后一次在其显示的语言环境中编辑的时间
     *
     * @field edited_at
     * @type bigint unsigned
     */
    editedAt: string | ORM.IRawSQL;

    /**
     * 文章链接
     *
     * @field html_url
     * @type varchar(255)
     */
    htmlUrl: string | ORM.IRawSQL;

    /**
     * 文章唯一 id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 文章初始点踩数据
     *
     * @field init_down_votes
     * @type int unsigned
     */
    initDownVotes: number | ORM.IRawSQL;

    /**
     * 文章初始点赞数据
     *
     * @field init_up_votes
     * @type int unsigned
     */
    initUpVotes: number | ORM.IRawSQL;

    /**
     * 与文章关联的标签名称数组
     *
     * @field label_names
     * @type json
     */
    labelNames: Custom.TLabelNames | ORM.IRawSQL;

    /**
     * 文章修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    modifiedAt: string | ORM.IRawSQL;

    /**
     * 文章名称
     *
     * @field name
     * @type varchar(255)
     */
    name: string | ORM.IRawSQL;

    /**
     * 文章在文章列表中的位置
     *
     * @field position
     * @type int unsigned
     */
    position: number | ORM.IRawSQL;

    /**
     * 文章是否被推广
     *
     * @field promoted
     * @type tinyint
     */
    promoted: number | ORM.IRawSQL;

    /**
     * 从Zendesk爬取的文章原数据
     *
     * @field raw
     * @type json
     */
    raw: Custom.TRaw | ORM.IRawSQL;

    /**
     * 文章所属版块 id
     *
     * @field section_id
     * @type bigint unsigned
     */
    sectionId: string | ORM.IRawSQL;

    /**
     * 文章标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string | ORM.IRawSQL;
}

export {
    IArticlesCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IArticlesEntity {

    /**
     * 文章内容 HTML
     *
     * @field body
     * @type mediumtext
     */
    body: string;

    /**
     * 是否禁用评论功能
     *
     * @field comments_disabled
     * @type tinyint
     */
    commentsDisabled: number;

    /**
     * 文章附带的内容标签 id 列表
     *
     * @field content_tag_ids
     * @type json
     */
    contentTagIds: Custom.TContentTagIds;

    /**
     * 文章创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    createdAt: string;

    /**
     * 文章是否已被删除
     *
     * @field deleted
     * @type tinyint
     */
    deleted: number;

    /**
     * 是否为草稿
     *
     * @field draft
     * @type tinyint
     */
    draft: number;

    /**
     * 最后一次在其显示的语言环境中编辑的时间
     *
     * @field edited_at
     * @type bigint unsigned
     */
    editedAt: string;

    /**
     * 文章链接
     *
     * @field html_url
     * @type varchar(255)
     */
    htmlUrl: string;

    /**
     * 文章唯一 id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 文章初始点踩数据
     *
     * @field init_down_votes
     * @type int unsigned
     */
    initDownVotes: number;

    /**
     * 文章初始点赞数据
     *
     * @field init_up_votes
     * @type int unsigned
     */
    initUpVotes: number;

    /**
     * 与文章关联的标签名称数组
     *
     * @field label_names
     * @type json
     */
    labelNames: Custom.TLabelNames;

    /**
     * 文章修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    modifiedAt: string;

    /**
     * 文章名称
     *
     * @field name
     * @type varchar(255)
     */
    name: string;

    /**
     * 文章在文章列表中的位置
     *
     * @field position
     * @type int unsigned
     */
    position: number;

    /**
     * 文章是否被推广
     *
     * @field promoted
     * @type tinyint
     */
    promoted: number;

    /**
     * 从Zendesk爬取的文章原数据
     *
     * @field raw
     * @type json
     */
    raw: Custom.TRaw;

    /**
     * 文章所属版块 id
     *
     * @field section_id
     * @type bigint unsigned
     */
    sectionId: string;

    /**
     * 文章标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string;
}

export {
    IArticlesEntity as IEntity,
};
export type IRepository = ORM.IRepository<IArticlesEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IArticlesIndexPRIMARY {

    /**
     * 文章唯一 id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'body' | 'commentsDisabled' | 'contentTagIds' | 'createdAt' | 'deleted' | 'draft' | 'editedAt' | 'htmlUrl' | 'id' | 'initDownVotes' | 'initUpVotes' | 'labelNames' | 'modifiedAt' | 'name' | 'position' | 'promoted' | 'raw' | 'sectionId' | 'title';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'body' | 'commentsDisabled' | 'contentTagIds' | 'createdAt' | 'deleted' | 'draft' | 'editedAt' | 'htmlUrl' | 'id' | 'initDownVotes' | 'initUpVotes' | 'labelNames' | 'modifiedAt' | 'name' | 'position' | 'promoted' | 'raw' | 'sectionId' | 'title';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'body' | 'comments_disabled' | 'content_tag_ids' | 'created_at' | 'deleted' | 'draft' | 'edited_at' | 'html_url' | 'id' | 'init_down_votes' | 'init_up_votes' | 'label_names' | 'modified_at' | 'name' | 'position' | 'promoted' | 'raw' | 'section_id' | 'title';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'body' | 'commentsDisabled' | 'contentTagIds' | 'createdAt' | 'deleted' | 'draft' | 'editedAt' | 'htmlUrl' | 'id' | 'initDownVotes' | 'initUpVotes' | 'labelNames' | 'modifiedAt' | 'name' | 'position' | 'promoted' | 'raw' | 'sectionId' | 'title';