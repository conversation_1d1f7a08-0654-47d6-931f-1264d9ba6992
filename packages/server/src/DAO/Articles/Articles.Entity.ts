/* eslint-disable */
import * as I from './Articles.Types';
import * as Custom from './Articles.Custom';
import * as ORM from '@reolink-fx/orm';
const ALL_MUTABLE_PROPS: Record<I.TEditableProperties, () => string> = Object.freeze({
    'body': () => AbstractArticlesEntity.refInsertValue("body"),
    'commentsDisabled': () => AbstractArticlesEntity.refInsertValue("commentsDisabled"),
    'contentTagIds': () => AbstractArticlesEntity.refInsertValue("contentTagIds"),
    'createdAt': () => AbstractArticlesEntity.refInsertValue("createdAt"),
    'deleted': () => AbstractArticlesEntity.refInsertValue("deleted"),
    'draft': () => AbstractArticlesEntity.refInsertValue("draft"),
    'editedAt': () => AbstractArticlesEntity.refInsertValue("editedAt"),
    'htmlUrl': () => AbstractArticlesEntity.refInsertValue("htmlUrl"),
    'id': () => AbstractArticlesEntity.refInsertValue("id"),
    'initDownVotes': () => AbstractArticlesEntity.refInsertValue("initDownVotes"),
    'initUpVotes': () => AbstractArticlesEntity.refInsertValue("initUpVotes"),
    'labelNames': () => AbstractArticlesEntity.refInsertValue("labelNames"),
    'modifiedAt': () => AbstractArticlesEntity.refInsertValue("modifiedAt"),
    'name': () => AbstractArticlesEntity.refInsertValue("name"),
    'position': () => AbstractArticlesEntity.refInsertValue("position"),
    'promoted': () => AbstractArticlesEntity.refInsertValue("promoted"),
    'raw': () => AbstractArticlesEntity.refInsertValue("raw"),
    'sectionId': () => AbstractArticlesEntity.refInsertValue("sectionId"),
    'title': () => AbstractArticlesEntity.refInsertValue("title"),
});

export abstract class AbstractArticlesEntity {

    /**
     * 该实体类是否对应一个模版表。
     */
    public static readonly isTemplate: boolean = false;


    /**
     * 在当前 DAO Entity 中，从属性到数据库字段名称的映射表。
     */
    public static readonly propertyToFieldMapping: Readonly<Record<I.TProperties, I.TFields>> = Object.freeze({
        'body': 'body',
        'commentsDisabled': 'comments_disabled',
        'contentTagIds': 'content_tag_ids',
        'createdAt': 'created_at',
        'deleted': 'deleted',
        'draft': 'draft',
        'editedAt': 'edited_at',
        'htmlUrl': 'html_url',
        'id': 'id',
        'initDownVotes': 'init_down_votes',
        'initUpVotes': 'init_up_votes',
        'labelNames': 'label_names',
        'modifiedAt': 'modified_at',
        'name': 'name',
        'position': 'position',
        'promoted': 'promoted',
        'raw': 'raw',
        'sectionId': 'section_id',
        'title': 'title',
    });

    /**
     * 将一个标识符转换为可以安全引用的形式。
     */
    public static readonly quoteIdentifier = (identityName: string) => `\`${identityName}\``;

    /**
     * 在当前 DAO Entity 中，从数据库字段名称到属性的映射表。
     */
    public static readonly fieldToPropertyMapping: Readonly<Record<I.TFields, I.TProperties>> = Object.freeze({
        'body': 'body',
        'comments_disabled': 'commentsDisabled',
        'content_tag_ids': 'contentTagIds',
        'created_at': 'createdAt',
        'deleted': 'deleted',
        'draft': 'draft',
        'edited_at': 'editedAt',
        'html_url': 'htmlUrl',
        'id': 'id',
        'init_down_votes': 'initDownVotes',
        'init_up_votes': 'initUpVotes',
        'label_names': 'labelNames',
        'modified_at': 'modifiedAt',
        'name': 'name',
        'position': 'position',
        'promoted': 'promoted',
        'raw': 'raw',
        'section_id': 'sectionId',
        'title': 'title',
    });

    /**
     * 插入一行数据到数据库，成功时返回 true。
     *
     * > 当由于唯一索引冲突导致失败时，如果 `ignoreOnDup === true`，那么返回 false，否则抛出异常。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param ignoreOnDup          是否在唯一索引冲突时放弃插入操作（不抛出异常）
     */
    public static async insert(repo: I.IRepository, newRecord: I.ICreation, ignoreOnDup: boolean = false): Promise<boolean> {

        let req = repo.createQuery().insert().values(newRecord);
        const [sql, params] = (ignoreOnDup ? req.orIgnore() : req).getQueryAndParameters();
        return !!(await repo.execute(sql, params))?.affectedRows;
    }

    /**
     * 批量插入数据到数据库。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param ignoreOnDup          是否在唯一索引冲突时放弃插入操作（不抛出异常）
     */
    public static async bulkInsert(repo: I.IRepository, newRecord: Array<I.ICreation>, ignoreOnDup: boolean = false): Promise<void> {

        if (!Array.isArray(newRecord) || !newRecord.length) {
            return;
        }
        let req = repo.createQuery().insert().values(newRecord);
        const [sql, params] = (ignoreOnDup ? req.orIgnore() : req).getQueryAndParameters();
        await repo.execute(sql, params);
    }

    /**
     * 插入一行数据到数据库，并获取插入的行数据。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     */
    public static async insertAndGet(repo: I.IRepository, newRecord: I.ICreation): Promise<I.IEntity> {

        let req = repo.createQuery().insert().values(newRecord);
        await repo.execute(...req.getQueryAndParameters());
        return (await repo.findOne({ where: {
            'id': newRecord.id as any,
        } }))!;
    }

    /**
     * 插入一行数据到数据库，并在发生唯一索引冲突时更新特定字段。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param set                  发生唯一索引冲突时要更新的字段及对应数据
     */
    public static async upsert(repo: I.IRepository, newRecord: I.ICreation | Array<I.ICreation>, set: ORM.QueryDeepPartialEntity<Pick<I.IEntity, I.TEditableProperties>> | "all" | Array<I.TEditableProperties>): Promise<ORM.IExecutionResult> {

        if (Array.isArray(newRecord) && !newRecord.length) {
            return { affectedRows: 0, lastInsertId: 0 };
        }
        if (Array.isArray(set)) {
            set = set.reduce<ORM.QueryDeepPartialEntity<Pick<I.IEntity, I.TEditableProperties>>>((p, q) => ((p[q] = ALL_MUTABLE_PROPS[q]), p), {});
        }
        else if (set === "all") {
            set = ALL_MUTABLE_PROPS
        }
        let [iSQL, iParams] = repo.createQuery().insert().values(newRecord).getQueryAndParameters();
        const [uSQL, uParams] = repo.createQuery().update().set(set).getQueryAndParameters();
        iSQL += ' ON DUPLICATE KEY UPDATE ' + uSQL.slice(uSQL.indexOf('SET ') + 4);
        return repo.execute(iSQL, [...iParams, ...uParams]);
    }

    /**
     * 插入一行数据到数据库，并在发生唯一索引冲突时更新特定字段。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param set                  发生唯一索引冲突时要更新的字段及对应数据
     * @deprecated 使用 `upsert` 代替
     */
    public static readonly insertOrUpdateOnDup = this.upsert;

    /**
     * 生成一个解除了 `this` 绑定的 `refInsertValue` 工具函数。
     *
     * @deprecated 可直接引用 `refInsertValue` 属性。
     */
    public static createInsertValueRefer(): (p: I.TProperties) => string {
        return this.refInsertValue;
    }

    /**
     * 在 upsert 方法中使用，可以获取每一行插入数据中某个字段的值引用。
     *
     * > 如 `refInsertValue('qty')` 将变成 `VALUES([qty])`。 
     *
     * @param p      被引用的属性名称（不是数据库字段名称）
     */
    public static readonly refInsertValue = (p: I.TProperties) => `VALUES(${this.quoteIdentifier(this.propertyToFieldMapping[p])})`;

    /**
     * 生成一个解除了 `this` 绑定的 `refRowValue` 工具函数。
     *
     * @deprecated 可直接引用 `refRowValue` 属性。
     */
    public static createRowValueRefer(): (p: I.TProperties) => string {
        return this.refRowValue;
    }

    /**
     * 在 upsert/update 等方法中使用，可以获取被更新的每一行数据中某个字段的值引用。
     *
     * > 如 `refRowValue('qty')` 将变成 `[qty]`。 
     *
     * @param p      被引用的属性名称（不是数据库字段名称）
     */
    public static readonly refRowValue = (p: I.TProperties) => this.quoteIdentifier(this.propertyToFieldMapping[p])
}

@ORM.Entity('articles')
export class ArticlesEntity extends AbstractArticlesEntity implements I.IEntity {

    /**
     * 文章内容 HTML
     *
     * @field body
     * @type mediumtext
     */
    @ORM.Column('mediumtext', {
        'name': 'body',
    })
    public body!: string;

    /**
     * 是否禁用评论功能
     *
     * @field comments_disabled
     * @type tinyint
     */
    @ORM.Column('tinyint', {
        'name': 'comments_disabled',
    })
    public commentsDisabled!: number;

    /**
     * 文章附带的内容标签 id 列表
     *
     * @field content_tag_ids
     * @type json
     */
    @ORM.Column('json', {
        'name': 'content_tag_ids',
    })
    public contentTagIds!: Custom.TContentTagIds;

    /**
     * 文章创建时间
     *
     * @field created_at
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'created_at',
        'unsigned': true,
    })
    public createdAt!: string;

    /**
     * 文章是否已被删除
     *
     * @field deleted
     * @type tinyint
     */
    @ORM.Column('tinyint', {
        'name': 'deleted',
    })
    public deleted!: number;

    /**
     * 是否为草稿
     *
     * @field draft
     * @type tinyint
     */
    @ORM.Column('tinyint', {
        'name': 'draft',
    })
    public draft!: number;

    /**
     * 最后一次在其显示的语言环境中编辑的时间
     *
     * @field edited_at
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'edited_at',
        'unsigned': true,
    })
    public editedAt!: string;

    /**
     * 文章链接
     *
     * @field html_url
     * @type varchar(255)
     */
    @ORM.Column('varchar', {
        'name': 'html_url',
        'length': 255,
    })
    public htmlUrl!: string;

    /**
     * 文章唯一 id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    @ORM.PrimaryColumn('bigint', {
        'name': 'id',
        'unsigned': true,
    })
    public id!: string;

    /**
     * 文章初始点踩数据
     *
     * @field init_down_votes
     * @type int unsigned
     */
    @ORM.Column('int', {
        'name': 'init_down_votes',
        'unsigned': true,
    })
    public initDownVotes!: number;

    /**
     * 文章初始点赞数据
     *
     * @field init_up_votes
     * @type int unsigned
     */
    @ORM.Column('int', {
        'name': 'init_up_votes',
        'unsigned': true,
    })
    public initUpVotes!: number;

    /**
     * 与文章关联的标签名称数组
     *
     * @field label_names
     * @type json
     */
    @ORM.Column('json', {
        'name': 'label_names',
    })
    public labelNames!: Custom.TLabelNames;

    /**
     * 文章修改时间
     *
     * @field modified_at
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'modified_at',
        'unsigned': true,
    })
    public modifiedAt!: string;

    /**
     * 文章名称
     *
     * @field name
     * @type varchar(255)
     */
    @ORM.Column('varchar', {
        'name': 'name',
        'length': 255,
    })
    public name!: string;

    /**
     * 文章在文章列表中的位置
     *
     * @field position
     * @type int unsigned
     */
    @ORM.Column('int', {
        'name': 'position',
        'unsigned': true,
    })
    public position!: number;

    /**
     * 文章是否被推广
     *
     * @field promoted
     * @type tinyint
     */
    @ORM.Column('tinyint', {
        'name': 'promoted',
    })
    public promoted!: number;

    /**
     * 从Zendesk爬取的文章原数据
     *
     * @field raw
     * @type json
     */
    @ORM.Column('json', {
        'name': 'raw',
    })
    public raw!: Custom.TRaw;

    /**
     * 文章所属版块 id
     *
     * @field section_id
     * @type bigint unsigned
     */
    @ORM.Column('bigint', {
        'name': 'section_id',
        'unsigned': true,
    })
    public sectionId!: string;

    /**
     * 文章标题
     *
     * @field title
     * @type varchar(255)
     */
    @ORM.Column('varchar', {
        'name': 'title',
        'length': 255,
    })
    public title!: string;
}

export {
    ArticlesEntity as Entity,
};
