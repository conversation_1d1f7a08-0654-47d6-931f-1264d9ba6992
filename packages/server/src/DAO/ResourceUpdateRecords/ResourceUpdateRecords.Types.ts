/* eslint-disable */
import * as ORM from '@reolink-fx/orm';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IResourceUpdateRecordsCreation {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 资源类型 1：聚合页 2：文章
     *
     * @field resource
     * @type tinyint
     * @unique
     */
    resource: number | ORM.IRawSQL;

    /**
     * 资源更新时间
     *
     * @field updated_at
     * @type bigint unsigned
     */
    updatedAt: string | ORM.IRawSQL;
}

export {
    IResourceUpdateRecordsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IResourceUpdateRecordsEntity {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 资源类型 1：聚合页 2：文章
     *
     * @field resource
     * @type tinyint
     * @unique
     */
    resource: number;

    /**
     * 资源更新时间
     *
     * @field updated_at
     * @type bigint unsigned
     */
    updatedAt: string;
}

export {
    IResourceUpdateRecordsEntity as IEntity,
};
export type IRepository = ORM.IRepository<IResourceUpdateRecordsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IResourceUpdateRecordsIndexPRIMARY {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index uk_resource
 * @unique
 * @property resource
 */
export interface IResourceUpdateRecordsIndexUkResource {

    /**
     * 资源类型 1：聚合页 2：文章
     *
     * @field resource
     * @type tinyint
     * @unique
     */
    resource: number;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'id' | 'resource' | 'updatedAt';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'id' | 'resource' | 'updatedAt';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'id' | 'resource' | 'updated_at';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'id' | 'resource' | 'updatedAt';