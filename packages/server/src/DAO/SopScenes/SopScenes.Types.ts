/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './SopScenes.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface ISopScenesCreation {

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * 创建人
     *
     * @field created_by
     * @type bigint
     */
    createdBy: string | ORM.IRawSQL;

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 元数据
     *
     * @field metadata
     * @type json
     */
    metadata: Custom.TMetadata | ORM.IRawSQL;

    /**
     * 更新时间
     *
     * @field modified_at
     * @type bigint
     */
    modifiedAt: string | ORM.IRawSQL;

    /**
     * 更新人
     *
     * @field modified_by
     * @type bigint
     */
    modifiedBy: string | ORM.IRawSQL;

    /**
     * 场景
     *
     * @field scene
     * @type varchar(512)
     * @unique
     */
    scene: string | ORM.IRawSQL;
}

export {
    ISopScenesCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface ISopScenesEntity {

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint
     */
    createdAt: string;

    /**
     * 创建人
     *
     * @field created_by
     * @type bigint
     */
    createdBy: string;

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 元数据
     *
     * @field metadata
     * @type json
     */
    metadata: Custom.TMetadata;

    /**
     * 更新时间
     *
     * @field modified_at
     * @type bigint
     */
    modifiedAt: string;

    /**
     * 更新人
     *
     * @field modified_by
     * @type bigint
     */
    modifiedBy: string;

    /**
     * 场景
     *
     * @field scene
     * @type varchar(512)
     * @unique
     */
    scene: string;
}

export {
    ISopScenesEntity as IEntity,
};
export type IRepository = ORM.IRepository<ISopScenesEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface ISopScenesIndexPRIMARY {

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index udx_scene
 * @unique
 * @property scene
 */
export interface ISopScenesIndexUdxScene {

    /**
     * 场景
     *
     * @field scene
     * @type varchar(512)
     * @unique
     */
    scene: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'createdAt' | 'createdBy' | 'id' | 'metadata' | 'modifiedAt' | 'modifiedBy' | 'scene';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'createdAt' | 'createdBy' | 'id' | 'metadata' | 'modifiedAt' | 'modifiedBy' | 'scene';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'created_at' | 'created_by' | 'id' | 'metadata' | 'modified_at' | 'modified_by' | 'scene';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'createdAt' | 'createdBy' | 'id' | 'metadata' | 'modifiedAt' | 'modifiedBy' | 'scene';