/* eslint-disable */
import * as I from './SopSteps.Types';
import * as Custom from './SopSteps.Custom';
import * as ORM from '@reolink-fx/orm';
const ALL_MUTABLE_PROPS: Record<I.TEditableProperties, () => string> = Object.freeze({
    'createdAt': () => AbstractSopStepsEntity.refInsertValue("createdAt"),
    'createdBy': () => AbstractSopStepsEntity.refInsertValue("createdBy"),
    'id': () => AbstractSopStepsEntity.refInsertValue("id"),
    'modifiedAt': () => AbstractSopStepsEntity.refInsertValue("modifiedAt"),
    'modifiedBy': () => AbstractSopStepsEntity.refInsertValue("modifiedBy"),
    'options': () => AbstractSopStepsEntity.refInsertValue("options"),
    'step': () => AbstractSopStepsEntity.refInsertValue("step"),
});

export abstract class AbstractSopStepsEntity {

    /**
     * 该实体类是否对应一个模版表。
     */
    public static readonly isTemplate: boolean = false;


    /**
     * 在当前 DAO Entity 中，从属性到数据库字段名称的映射表。
     */
    public static readonly propertyToFieldMapping: Readonly<Record<I.TProperties, I.TFields>> = Object.freeze({
        'createdAt': 'created_at',
        'createdBy': 'created_by',
        'id': 'id',
        'modifiedAt': 'modified_at',
        'modifiedBy': 'modified_by',
        'options': 'options',
        'step': 'step',
    });

    /**
     * 将一个标识符转换为可以安全引用的形式。
     */
    public static readonly quoteIdentifier = (identityName: string) => `\`${identityName}\``;

    /**
     * 在当前 DAO Entity 中，从数据库字段名称到属性的映射表。
     */
    public static readonly fieldToPropertyMapping: Readonly<Record<I.TFields, I.TProperties>> = Object.freeze({
        'created_at': 'createdAt',
        'created_by': 'createdBy',
        'id': 'id',
        'modified_at': 'modifiedAt',
        'modified_by': 'modifiedBy',
        'options': 'options',
        'step': 'step',
    });

    /**
     * 插入一行数据到数据库，成功时返回 true。
     *
     * > 当由于唯一索引冲突导致失败时，如果 `ignoreOnDup === true`，那么返回 false，否则抛出异常。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param ignoreOnDup          是否在唯一索引冲突时放弃插入操作（不抛出异常）
     */
    public static async insert(repo: I.IRepository, newRecord: I.ICreation, ignoreOnDup: boolean = false): Promise<boolean> {

        let req = repo.createQuery().insert().values(newRecord);
        const [sql, params] = (ignoreOnDup ? req.orIgnore() : req).getQueryAndParameters();
        return !!(await repo.execute(sql, params))?.affectedRows;
    }

    /**
     * 批量插入数据到数据库。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param ignoreOnDup          是否在唯一索引冲突时放弃插入操作（不抛出异常）
     */
    public static async bulkInsert(repo: I.IRepository, newRecord: Array<I.ICreation>, ignoreOnDup: boolean = false): Promise<void> {

        if (!Array.isArray(newRecord) || !newRecord.length) {
            return;
        }
        let req = repo.createQuery().insert().values(newRecord);
        const [sql, params] = (ignoreOnDup ? req.orIgnore() : req).getQueryAndParameters();
        await repo.execute(sql, params);
    }

    /**
     * 插入一行数据到数据库，并获取插入的行数据。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     */
    public static async insertAndGet(repo: I.IRepository, newRecord: I.ICreation): Promise<I.IEntity> {

        let req = repo.createQuery().insert().values(newRecord);
        await repo.execute(...req.getQueryAndParameters());
        return (await repo.findOne({ where: {
            'id': newRecord.id as any,
        } }))!;
    }

    /**
     * 插入一行数据到数据库，并在发生唯一索引冲突时更新特定字段。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param set                  发生唯一索引冲突时要更新的字段及对应数据
     */
    public static async upsert(repo: I.IRepository, newRecord: I.ICreation | Array<I.ICreation>, set: ORM.QueryDeepPartialEntity<Pick<I.IEntity, I.TEditableProperties>> | "all" | Array<I.TEditableProperties>): Promise<ORM.IExecutionResult> {

        if (Array.isArray(newRecord) && !newRecord.length) {
            return { affectedRows: 0, lastInsertId: 0 };
        }
        if (Array.isArray(set)) {
            set = set.reduce<ORM.QueryDeepPartialEntity<Pick<I.IEntity, I.TEditableProperties>>>((p, q) => ((p[q] = ALL_MUTABLE_PROPS[q]), p), {});
        }
        else if (set === "all") {
            set = ALL_MUTABLE_PROPS
        }
        let [iSQL, iParams] = repo.createQuery().insert().values(newRecord).getQueryAndParameters();
        const [uSQL, uParams] = repo.createQuery().update().set(set).getQueryAndParameters();
        iSQL += ' ON DUPLICATE KEY UPDATE ' + uSQL.slice(uSQL.indexOf('SET ') + 4);
        return repo.execute(iSQL, [...iParams, ...uParams]);
    }

    /**
     * 插入一行数据到数据库，并在发生唯一索引冲突时更新特定字段。
     *
     * @param repo                 数据表操作对象
     * @param newRecord            待插入的数据
     * @param set                  发生唯一索引冲突时要更新的字段及对应数据
     * @deprecated 使用 `upsert` 代替
     */
    public static readonly insertOrUpdateOnDup = this.upsert;

    /**
     * 生成一个解除了 `this` 绑定的 `refInsertValue` 工具函数。
     *
     * @deprecated 可直接引用 `refInsertValue` 属性。
     */
    public static createInsertValueRefer(): (p: I.TProperties) => string {
        return this.refInsertValue;
    }

    /**
     * 在 upsert 方法中使用，可以获取每一行插入数据中某个字段的值引用。
     *
     * > 如 `refInsertValue('qty')` 将变成 `VALUES([qty])`。 
     *
     * @param p      被引用的属性名称（不是数据库字段名称）
     */
    public static readonly refInsertValue = (p: I.TProperties) => `VALUES(${this.quoteIdentifier(this.propertyToFieldMapping[p])})`;

    /**
     * 生成一个解除了 `this` 绑定的 `refRowValue` 工具函数。
     *
     * @deprecated 可直接引用 `refRowValue` 属性。
     */
    public static createRowValueRefer(): (p: I.TProperties) => string {
        return this.refRowValue;
    }

    /**
     * 在 upsert/update 等方法中使用，可以获取被更新的每一行数据中某个字段的值引用。
     *
     * > 如 `refRowValue('qty')` 将变成 `[qty]`。 
     *
     * @param p      被引用的属性名称（不是数据库字段名称）
     */
    public static readonly refRowValue = (p: I.TProperties) => this.quoteIdentifier(this.propertyToFieldMapping[p])
}

@ORM.Index('udx_step', ['step'], { unique: true })
@ORM.Entity('sop_steps')
export class SopStepsEntity extends AbstractSopStepsEntity implements I.IEntity {

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint
     */
    @ORM.Column('bigint', {
        'name': 'created_at',
    })
    public createdAt!: string;

    /**
     * 创建人
     *
     * @field created_by
     * @type bigint
     */
    @ORM.Column('bigint', {
        'name': 'created_by',
    })
    public createdBy!: string;

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    @ORM.PrimaryColumn('bigint', {
        'name': 'id',
        'unsigned': true,
    })
    public id!: string;

    /**
     * 更新时间
     *
     * @field modified_at
     * @type bigint
     */
    @ORM.Column('bigint', {
        'name': 'modified_at',
    })
    public modifiedAt!: string;

    /**
     * 更新人
     *
     * @field modified_by
     * @type bigint
     */
    @ORM.Column('bigint', {
        'name': 'modified_by',
    })
    public modifiedBy!: string;

    /**
     * 步骤可触发的option
     *
     * @field options
     * @type json
     */
    @ORM.Column('json', {
        'name': 'options',
    })
    public options!: Custom.TOptions;

    /**
     * 步骤
     *
     * @field step
     * @type varchar(512)
     * @unique
     */
    @ORM.Column('varchar', {
        'name': 'step',
        'length': 512,
    })
    public step!: string;
}

export {
    SopStepsEntity as Entity,
};
