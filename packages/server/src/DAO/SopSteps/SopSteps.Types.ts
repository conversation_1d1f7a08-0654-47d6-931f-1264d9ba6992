/* eslint-disable */
import * as ORM from '@reolink-fx/orm';
import * as Custom from './SopSteps.Custom';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface ISopStepsCreation {

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint
     */
    createdAt: string | ORM.IRawSQL;

    /**
     * 创建人
     *
     * @field created_by
     * @type bigint
     */
    createdBy: string | ORM.IRawSQL;

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 更新时间
     *
     * @field modified_at
     * @type bigint
     */
    modifiedAt: string | ORM.IRawSQL;

    /**
     * 更新人
     *
     * @field modified_by
     * @type bigint
     */
    modifiedBy: string | ORM.IRawSQL;

    /**
     * 步骤可触发的option
     *
     * @field options
     * @type json
     */
    options: Custom.TOptions | ORM.IRawSQL;

    /**
     * 步骤
     *
     * @field step
     * @type varchar(512)
     * @unique
     */
    step: string | ORM.IRawSQL;
}

export {
    ISopStepsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface ISopStepsEntity {

    /**
     * 创建时间
     *
     * @field created_at
     * @type bigint
     */
    createdAt: string;

    /**
     * 创建人
     *
     * @field created_by
     * @type bigint
     */
    createdBy: string;

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 更新时间
     *
     * @field modified_at
     * @type bigint
     */
    modifiedAt: string;

    /**
     * 更新人
     *
     * @field modified_by
     * @type bigint
     */
    modifiedBy: string;

    /**
     * 步骤可触发的option
     *
     * @field options
     * @type json
     */
    options: Custom.TOptions;

    /**
     * 步骤
     *
     * @field step
     * @type varchar(512)
     * @unique
     */
    step: string;
}

export {
    ISopStepsEntity as IEntity,
};
export type IRepository = ORM.IRepository<ISopStepsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface ISopStepsIndexPRIMARY {

    /**
     * ID
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index udx_step
 * @unique
 * @property step
 */
export interface ISopStepsIndexUdxStep {

    /**
     * 步骤
     *
     * @field step
     * @type varchar(512)
     * @unique
     */
    step: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'createdAt' | 'createdBy' | 'id' | 'modifiedAt' | 'modifiedBy' | 'options' | 'step';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'createdAt' | 'createdBy' | 'id' | 'modifiedAt' | 'modifiedBy' | 'options' | 'step';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'created_at' | 'created_by' | 'id' | 'modified_at' | 'modified_by' | 'options' | 'step';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'createdAt' | 'createdBy' | 'id' | 'modifiedAt' | 'modifiedBy' | 'options' | 'step';