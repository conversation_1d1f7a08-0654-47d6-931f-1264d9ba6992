/* eslint-disable */
import * as ORM from '@reolink-fx/orm';

/**
 * 插入新纪录时所需填写的数据。
 */
export interface IAggregationPagesItemsCreation {

    /**
     * 聚合页栏目锚点
     *
     * @field anchor
     * @type varchar(255)
     * @unique
     */
    anchor: string | ORM.IRawSQL;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string | ORM.IRawSQL;

    /**
     * 聚合页id
     *
     * @field page_id
     * @type bigint unsigned
     */
    pageId: string | ORM.IRawSQL;

    /**
     * 栏目结构
     *
     * @field structure
     * @type varchar(128)
     */
    structure: string | ORM.IRawSQL;

    /**
     * 聚合页栏目标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string | ORM.IRawSQL;

    /**
     * 聚合页类型
     *
     * @field type
     * @type tinyint
     */
    type: number | ORM.IRawSQL;
}

export {
    IAggregationPagesItemsCreation as ICreation,
};

/**
 * 记录的纯数据类型。
 */
export interface IAggregationPagesItemsEntity {

    /**
     * 聚合页栏目锚点
     *
     * @field anchor
     * @type varchar(255)
     * @unique
     */
    anchor: string;

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;

    /**
     * 聚合页id
     *
     * @field page_id
     * @type bigint unsigned
     */
    pageId: string;

    /**
     * 栏目结构
     *
     * @field structure
     * @type varchar(128)
     */
    structure: string;

    /**
     * 聚合页栏目标题
     *
     * @field title
     * @type varchar(255)
     */
    title: string;

    /**
     * 聚合页类型
     *
     * @field type
     * @type tinyint
     */
    type: number;
}

export {
    IAggregationPagesItemsEntity as IEntity,
};
export type IRepository = ORM.IRepository<IAggregationPagesItemsEntity>;

/**
 * @index PRIMARY
 * @unique
 * @property id
 */
export interface IAggregationPagesItemsIndexPRIMARY {

    /**
     * id
     *
     * @field id
     * @type bigint unsigned
     * @primary
     */
    id: string;
}

/**
 * @index uk_anchor
 * @unique
 * @property anchor
 */
export interface IAggregationPagesItemsIndexUkAnchor {

    /**
     * 聚合页栏目锚点
     *
     * @field anchor
     * @type varchar(255)
     * @unique
     */
    anchor: string;
}



/**
 * 可以编辑修改的属性名称集合。
 */
export type TEditableProperties = 'anchor' | 'id' | 'pageId' | 'structure' | 'title' | 'type';

/**
 * 所有的属性名称集合。
 */
export type TProperties = 'anchor' | 'id' | 'pageId' | 'structure' | 'title' | 'type';

/**
 * 所有的数据库字段名称集合。
 */
export type TFields = 'anchor' | 'id' | 'page_id' | 'structure' | 'title' | 'type';

/**
 * 创建时必须填写的属性名称集合。
 */
export type TRequiredProperties = 'anchor' | 'id' | 'pageId' | 'structure' | 'title' | 'type';