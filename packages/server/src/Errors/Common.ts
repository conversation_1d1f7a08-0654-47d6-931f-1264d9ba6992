import * as GatewaySdk from '@reolink-services/api-gateway';

export const E_ENCRYPT_FAILED = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'encrypt_failed';

    public static override message = 'Encrypt failed.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.BAD_REQUEST;
};

export const E_DECRYPT_FAILED = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'decrypt_failed';

    public static override message = 'Decrypt failed.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.BAD_REQUEST;
};
