import * as GatewaySdk from '@reolink-services/api-gateway';

export const E_SEND_MESSAGE_ERROR = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'send_message_error';

    public static override message = 'Send message error.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.INTERNAL_SERVER_ERROR;
};

export const E_GET_SWITCHBOARD_ERROR = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223345;

    public static override symbol = 'get_switchboard_error';

    public static override message = 'Get switchboard error.';
};

export const E_TRANSFER_ERROR = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223346;

    public static override symbol = 'transfer_error';

    public static override message = 'Transfer error.';
};

export const E_REQUEST_ZENDESK_ERROR = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223347;

    public static override symbol = 'request_zendesk_error';

    public static override message = 'Request zendesk error.';
};
