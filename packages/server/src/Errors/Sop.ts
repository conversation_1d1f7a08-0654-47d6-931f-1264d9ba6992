import * as GatewaySdk from '@reolink-services/api-gateway';

export const E_SCENE_ALREADY_USED = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'scene_already_used';

    public static override message = 'Scene is already used by sop.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.BAD_REQUEST;
};

export const E_STEP_ALREADY_USED = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'step_already_used';

    public static override message = 'Step is already used by sop.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.BAD_REQUEST;
};

export const E_DUP_SCENE = class extends GatewaySdk.PlatformApiError {

    public static override symbol = 'dup_scene';

    public static override message = 'Scene is already created.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.BAD_REQUEST;
};

export const E_DUP_STEP = class extends GatewaySdk.PlatformApiError {

    public static override symbol = 'dup_step';

    public static override message = 'Step is already created.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.BAD_REQUEST;
};

export const E_DUP_SOP = class extends GatewaySdk.PlatformApiError {

    public static override symbol = 'dup_sop';

    public static override message = 'Sop is already created.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.BAD_REQUEST;
};
