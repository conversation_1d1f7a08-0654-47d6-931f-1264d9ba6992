import * as Config from '@reolink-fx/config';
import * as _ from '@reolink-fx/utils';
import * as DI from '@reolink-fx/di';
import ChatSceneController from '#/Controllers/Internal/ChatSceneController';
import { EChatScene } from '#/Services/Decls/Chat';

export class ClassificationTest {

    private readonly _cfg = Config.useConfig<{
        input: string;
        output: string;
    }>({
        path: 'classificationFilePath'
    });

    private readonly _chatSceneMgr = DI.use(ChatSceneController);

    public async testClassification(): Promise<void> {

        const inputs = _.String.parseJSON<string>(await _.File.readTextFile(this._cfg.input), {
            onError: () => ''
        });
        const outputs: string[] = [];
        for (const input of inputs) {
            const output = await this._chatSceneMgr.chatWithScene({
                content: input,
                scene: EChatScene.CLASSIFICATION,

            });
            outputs.push(output.response);

        }
        await _.File.writeFile(this._cfg.output, JSON.stringify(outputs));

    }
}
