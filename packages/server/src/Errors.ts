import * as Rpc from '@reolink-fx/rpc';
import * as GatewaySdk from '@reolink-services/api-gateway';

export const E_TEST = class extends Rpc.RpcApiError {

    public static override symbol = 'test';

    public static override message = 'This is a rpc api error demo.';
};

export const E_LLM_SCENE_INACTIVE = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'llm_scene_inactive';

    public static override message = 'llm scene is inactive.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.FORBIDDEN;
};

export const E_CONVERSATION_NOT_FOUND = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'conversation_not_found';

    public static override message = 'conversation not found.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.FORBIDDEN;
};

export const E_PROMPT_NOT_FOUND = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'prompt_not_found';

    public static override message = 'prompt not found.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.FORBIDDEN;
};

export const E_CONVERSATION_ALREADY_FEEDBACK = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'conversation_already_feedback';

    public static override message = 'conversation already feedback.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.FORBIDDEN;
};

export const E_REACH_PER_DAY_REQUEST_LIMIT = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'reach_per_day_request_limit';

    public static override message = 'reach per day request limit.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.FORBIDDEN;
};

export const E_GET_EMBEDDING_ERROR = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'get_embedding_error';

    public static override message = 'get embedding error.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.INTERNAL_SERVER_ERROR;
};

export const E_CREATE_STREAM_CHAT_ERROR = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'create_stream_chat_error';

    public static override message = 'create stream chat error.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.INTERNAL_SERVER_ERROR;
};

export const E_CREATE_CHAT_ERROR = class extends GatewaySdk.PlatformApiError {

    public static override code = 0x11223344;

    public static override symbol = 'create_chat_error';

    public static override message = 'create chat error.';

    public static override httpStatusCode = GatewaySdk.EHttpStatusCode.INTERNAL_SERVER_ERROR;
};
