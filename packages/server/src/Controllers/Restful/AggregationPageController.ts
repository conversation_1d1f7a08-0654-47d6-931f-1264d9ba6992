import * as Rpc from '@reolink-fx/rpc';
import * as $R from '@reolink-services/ai-support.restful-apis';
import { IRestfulParameter } from '@reolink-services/api-gateway';
import * as DI from '@reolink-fx/di';
import * as GatewaySdk from '@reolink-services/api-gateway';
import { AggregationPageManager } from '#/Services/AggregationPageManager';

@Rpc.Controller()
export default class AggregationPageController implements Rpc.IApiController<$R.AggregationPage.IApiSet> {

    private readonly _aggregationPageMgr =  DI.use(AggregationPageManager);

    @GatewaySdk.RestfulApi({
        'routes': {
            'method': 'POST',
            'path': '/aibot/slug'
        },
        'requires': {
            'body': true,
            'auth': {
                'from': {
                    'clientUser': true,
                    'guest': true,
                    'deviceUser': false,
                    'deviceSelf': false
                }
            }
        },
        'validation': {
            'body': {
                'content': 'string(0,5000)',
                'productModel': 'string(1,255)'
            }
        }
    })
    public async getAggregationPageSlug(
        args: IRestfulParameter<$R.AggregationPage.IGetAggregationPageSlugArgs,  Record<string, unknown>>
    ): Promise<$R.AggregationPage.IAggregationPageSlugs> {

        const response = await this._aggregationPageMgr.detectSlug(args.body.content, args.body.productModel);

        return {
            'slugs': response
        };
    }

}
