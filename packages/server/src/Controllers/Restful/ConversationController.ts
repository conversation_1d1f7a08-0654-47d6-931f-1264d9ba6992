import * as Rpc from '@reolink-fx/rpc';
import * as $R from '@reolink-services/ai-support.restful-apis';
import { IRestfulParameter } from '@reolink-services/api-gateway';
import * as DI from '@reolink-fx/di';
import * as GatewaySdk from '@reolink-services/api-gateway';
import { ConversationManager } from '#/Services/ConversationManager';
import { ConversationRender } from '#/Views/ConversationRender';

@Rpc.Controller()
export default class ConversationController implements Rpc.IApiController<$R.Conversation.IApiSet> {

    private readonly _conversationMgr = DI.use(ConversationManager);

    private readonly _conversationRender = DI.use(ConversationRender);

    @GatewaySdk.RestfulApi({
        'routes': {
            'method': 'POST',
            'path': '/aibot/feedback'
        },
        'requires': {
            'body': true,
            'auth': {
                'from': {
                    'clientUser': true,
                    'guest': true,
                    'deviceUser': false,
                    'deviceSelf': false
                }
            }
        },
        'validation': {
            'body': {
                'conversationId': ['$.type', 'id', '~=/^\\d+$/'],
                'problemUnresolvedReasons->[]?': ['=too_much_irrelevant_information', '=not_consistent_with_the_facts',
                    '=provided_step_cannot_be_completed', '=waiting_time_is_too_long', '=page_interaction', '=other'],
                'issueResolved': 'boolean',
                'additionalComments?': 'string(0,255)'
            }
        }
    })
    public async createConversationFeedback(
        args: IRestfulParameter<$R.Conversation.ICreateConversationFeedbackBodyArgs,  Record<string, unknown>>
    ): Promise<Rpc.IOkResponse> {

        await this._conversationMgr.createConversationFeedback({
            ...args.body,
            problemUnresolvedReasons: args.body.problemUnresolvedReasons?.map((v) =>
                this._conversationRender.reasonStr2Enum(v))
        });

        return Rpc.OK_RESPONSE;
    }

}
