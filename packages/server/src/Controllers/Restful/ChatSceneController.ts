import * as GatewaySdk from '@reolink-services/api-gateway';
import * as Config from '@reolink-fx/config';
import * as _ from '@reolink-fx/utils';
import * as Rpc from '@reolink-fx/rpc';
import * as $R from '@reolink-services/ai-support.restful-apis';
import { IRestfulParameter } from '@reolink-services/api-gateway';
import { E_LLM_SCENE_INACTIVE } from '#/Errors';
import * as DI from '@reolink-fx/di';
import { ChatSceneManager } from '#/Services/ChatSceneManager';
import { ConversationManager } from '#/Services/ConversationManager';
import { ThrottlingManager } from '#/Services/ThrottlingManager';
import { ChatManager } from '#/Services/ChatManager';

@Rpc.Controller()
export default class ChatSceneController implements Rpc.IApiController<$R.Chat.IApiSet> {

    private readonly _sceneConfig = Config.useConfig<Record<string, boolean>>({
        'path': 'sceneConfig'
    });

    private readonly _chatSceneMgr =  DI.use(ChatSceneManager);

    private readonly _throttlingMgr =  DI.use(ThrottlingManager);

    private readonly _conversationMgr =  DI.use(ConversationManager);

    private readonly _chatMgr = DI.use(ChatManager);

    @GatewaySdk.RestfulApi({
        'routes': {
            'method': 'POST',
            'path': '/aibot/receive-message'
        },
        'requires': {
            'body': true,
            'auth': {
                'from': {
                    'clientUser': true,
                    'guest': true,
                    'deviceUser': false,
                    'deviceSelf': false
                }
            }
        }
    })
    public async receiveMessage(
        args: IRestfulParameter<$R.Chat.IWebhookMessage,  Record<string, unknown>>
    ): Promise<Rpc.IOkResponse> {

        await this._chatMgr.handleWebhookMessage(args.body);

        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.RestfulApi({
        'routes': {
            'method': 'POST',
            'path': '/aibot/test/receive-message'
        },
        'requires': {
            'body': true,
            'auth': {
                'from': {
                    'clientUser': true,
                    'guest': true,
                    'deviceUser': false,
                    'deviceSelf': false
                }
            }
        }
    })
    public async receiveTestMessage(
        args: IRestfulParameter<$R.Chat.IWebhookMessage,  Record<string, unknown>>
    ): Promise<Rpc.IOkResponse> {

        await this._chatMgr.handleWebhookMessage(args.body, true);

        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.RestfulApi({
        'routes': {
            'method': 'POST',
            'path': '/aibot/scene/chat'
        },
        'requires': {
            'body': true,
            'auth': {
                'from': {
                    'clientUser': true,
                    'guest': true,
                    'deviceUser': false,
                    'deviceSelf': false
                }
            },
            'clientIP': true
        },
        'validation': {
            'body': {
                'scene': 'string(1,32)',
                'content': 'string(1,5000)',
                'params?': {}
            }
        }
    })
    public async chatWithScene(
        args: IRestfulParameter<$R.Chat.IChatWithSceneBodyArgs, Record<string, unknown>>,
        meta: Rpc.IMetadata
    ): Promise<$R.Chat.IChatWithSceneResponse> {

        const ip = GatewaySdk.getClientIpFromMetadata(meta);

        if (ip) {
            await this._throttlingMgr.addRequest(ip);
        }

        if (!this._sceneConfig[args.body.scene]) {
            throw new E_LLM_SCENE_INACTIVE();
        }

        const resp = await this._conversationMgr.createConversation({
            'content': args.body.content,
            'createdAt': Date.now().toString()
        },
        args.body.params ?? {},
        GatewaySdk.getAccountIdFromMetadata(meta));

        _.Async.invokeAsync(() => this._chatSceneMgr.oneRoundChat({
            ...args.body,
            'conversationId': resp.conversationId
        }));

        return {
            'conversationId': resp.conversationId
        };

    }

    @GatewaySdk.RestfulApi({
        'routes': {
            'method': 'GET',
            'path': '/aibot/scene/{scene:string}'
        },
        'requires': {
            'path': true,
            'auth': {
                'from': {
                    'clientUser': true,
                    'guest': true,
                    'deviceUser': false,
                    'deviceSelf': false
                }
            }
        },
        'validation': {
            'path': {
                'scene': 'string(1,32)'
            }
        }
    })
    public getChatSceneStatus(
        args: IRestfulParameter<unknown, $R.Chat.IGetChatSceneStatusPathArgs>
    ): $R.Chat.IGetChatSceneStatusResponse {

        return {
            'status': this._sceneConfig[args.path.scene] ? 'active' : 'inactive'
        };
    }

}
