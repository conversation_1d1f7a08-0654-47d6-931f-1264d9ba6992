import * as Rpc from '@reolink-fx/rpc';
import * as GatewaySdk from '@reolink-services/api-gateway';
import * as DI from '@reolink-fx/di';
import { SopSceneManager } from '#/Services/sop/SopSceneManager';
import * as $P from '@reolink-services/ai-support.platform-apis';
import { BuiltInTypes as $Types } from '@reolink-fx/typeguard';

@Rpc.Controller()
export default class SopSceneController implements $P.Scene.IApiSet {

    private readonly _sceneManager = DI.use(SopSceneManager);

    @GatewaySdk.TunnelApi({
        permissions: $P.Scene.PREM_CREATE_SCENE,
        validation: {
            'scene': 'string(1,512)'
        }
    })
    public async createScene(
        args: $P.Scene.ICreateSceneArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse> {

        await this._sceneManager.createScene(args, GatewaySdk.getAccountIdFromMetadata(meta, true));

        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Scene.PREM_UPDATE_SCENE,
        validation: {
            'sceneId': $Types.TYPE_SNOWFLAKE_ID,
            'scene?': 'string(1,512)'
        }
    })
    public async updateScene(
        args: $P.Scene.IUpdateSceneArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse> {
        await this._sceneManager.updateScene(
            args,
            GatewaySdk.getAccountIdFromMetadata(meta, true));

        return Rpc.OK_RESPONSE;

    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Scene.PREM_CREATE_SCENE,
        validation: {
            'sceneId': $Types.TYPE_SNOWFLAKE_ID
        }
    })
    public async deleteScene(
        args: $P.Scene.IDeleteSceneArgs,
    ): Promise<Rpc.IOkResponse> {
        await this._sceneManager.deleteScene(args.sceneId);
        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Scene.PREM_UPDATE_SCENE,
        validation: {
            'scene?': 'string(1,512)',
            ...GatewaySdk.createPageQueryArgsValidation({
                'maxPage': 1000,
                'maxLimit': 100
            })
        }
    })
    public async getSceneList(
        args: $P.Scene.IGetSceneListArgs
    ): Promise<Rpc.IListResponse<$P.Scene.IScene>> {
        const [scenes, totalRows] = await this._sceneManager.getSceneList(args);
        return {
            'totalRows': totalRows,
            'items': scenes.map(v => {
                return { ...v,
                    createdAt: parseInt(v.createdAt),
                    modifiedAt: parseInt(v.modifiedAt) };
            })
        };
    }

}
