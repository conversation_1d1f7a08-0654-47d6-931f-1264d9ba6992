import * as Rpc from '@reolink-fx/rpc';
import * as GatewaySdk from '@reolink-services/api-gateway';
import * as DI from '@reolink-fx/di';
import { SopManager } from '#/Services/sop/SopManager';
import * as $P from '@reolink-services/ai-support.platform-apis';
import { BuiltInTypes as $Types } from '@reolink-fx/typeguard';

@Rpc.Controller()
export default class SopController implements $P.Sop.IApiSet {

    private readonly _sopManager = DI.use(SopManager);

    @GatewaySdk.TunnelApi({
        permissions: $P.Sop.PREM_CREATE_SOP,
        validation: {
            'sceneId?': $Types.TYPE_SNOWFLAKE_ID,
            'tags': 'array',
            'intent': 'string(1,255)',
            'description': 'string(1,1000)',
            'solutions': {
                'steps->[]': {
                    'stepId': $Types.TYPE_SNOWFLAKE_ID,
                    'priority': 'number'
                }
            }
        }
    })
    public async createSop(
        args: $P.Sop.ICreateSopArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse> {
        await this._sopManager.createSop(
            args,
            GatewaySdk.getAccountIdFromMetadata(meta, true));
        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Sop.PREM_UPDATE_SOP,
        validation: {
            'sopId': $Types.TYPE_SNOWFLAKE_ID,
            'sceneId?': ['=0', $Types.TYPE_SNOWFLAKE_ID],
            'tags?': 'array',
            'intent?': 'string(1,255)',
            'description?': 'string(1,1000)',
            'solutions': {
                'steps->[]': {
                    'stepId': $Types.TYPE_SNOWFLAKE_ID,
                    'priority': 'number'
                }
            }
        }
    })
    public async updateSop(
        args: $P.Sop.IUpdateSopArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse> {
        await this._sopManager.updateSop(args, GatewaySdk.getAccountIdFromMetadata(meta, true));
        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Sop.PREM_DELETE_SOP,
        validation: {
            'sopId': $Types.TYPE_SNOWFLAKE_ID
        }
    })
    public async deleteSop(
        args: $P.Sop.IDeleteSopArgs
    ): Promise<Rpc.IOkResponse> {
        await this._sopManager.deleteSop(args.sopId);
        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Sop.PREM_GET_SOP,
        validation: {
            'ids?': {
                'type': 'array',
                'items': $Types.TYPE_SNOWFLAKE_ID
            },
            'intent?': 'string(1,255)',
            'tag?': 'string',
            ...GatewaySdk.createPageQueryArgsValidation({
                'maxPage': 1000,
                'maxLimit': 100
            })
        }
    })
    public async getSopList(
        args: $P.Sop.IGetSopListArgs
    ): Promise<Rpc.IListResponse<$P.Sop.IViewSop>> {
        const [sops, totalRows] = await this._sopManager.getSopList(args);
        return {
            'totalRows': totalRows,
            'items': sops.map(v => ({
                ...v,
                createdAt: parseInt(v.createdAt),
                modifiedAt: parseInt(v.modifiedAt)
            }))
        };
    }
}
