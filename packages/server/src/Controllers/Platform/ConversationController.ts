import * as Rpc from '@reolink-fx/rpc';
import * as $P from '@reolink-services/ai-support.platform-apis';
import * as GatewaySdk from '@reolink-services/api-gateway';
import * as DI from '@reolink-fx/di';
import { ConversationManager } from '#/Services/ConversationManager';
import { ConversationRender } from '#/Views/ConversationRender';
import * as _ from '@reolink-fx/utils';

@Rpc.Controller()
export default class ConversationController implements Rpc.IApiController<$P.Conversation.IApiSet> {

    private readonly _conversationMgr = DI.use(ConversationManager);

    private readonly _conversationRender = DI.use(ConversationRender);

    @GatewaySdk.TunnelApi({
        permissions: $P.Conversation.PREM_GET_CONVERSATION,
        validation: {
            'issueResolved?': 'boolean',
            'category?': ['=other', '=consultation', '=troubleshooting'],
            'emotion?': ['=neutral', '=negative', '=positive', '=extremely_negative'],
            'isPassToAgent?': 'boolean',
            'isCreateTicket?': 'boolean',
            'sopId?': 'string',
            'isWrongIntent?': 'boolean',
            ...Rpc.createPageQueryArgsValidation({
                'maxLimit': 100
            })
        }
    })
    public async getConversationList(
        args: $P.Conversation.IGetConversationListArgs
    ): Promise<Rpc.IListResponse<$P.Conversation.IConversation>> {

        const [conversations, totalRows, messages] = await this._conversationMgr.getConversationList({
            ...args,
            emotion: args.emotion ? this._conversationRender.emotionStr2Enum(args.emotion) : undefined,
            category: args.category ? this._conversationRender.categoryStr2Enum(args.category) : undefined,
        });

        const messageRecord = _.Array.toGroupArrayDict(messages, 'conversationId');

        return {
            items: conversations.map((v) => this._conversationRender.createConversationView(v, messageRecord[v.id])),
            totalRows: totalRows
        };
    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Conversation.PREM_GET_CONVERSATION,
        validation: {
            'isWrongIntent?': 'boolean',
            'agentTags->[]?': 'string',
            'agentComments?': 'string(1,255)'
        }
    })
    public async markConversation(
        args: $P.Conversation.IMarkConversationArgs
    ): Promise<Rpc.IOkResponse> {
        await this._conversationMgr.markConversation(args);
        return Rpc.OK_RESPONSE;
    }

}
