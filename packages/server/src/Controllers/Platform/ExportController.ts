import * as DI from '@reolink-fx/di';
import * as $P from '@reolink-services/ai-support.platform-apis';
import * as _ from '@reolink-fx/utils';
import * as Rpc from '@reolink-fx/rpc';
import { ConversationExporter, IExportConversationsArgs, IExportConversationsResponse } from '#/Services/Exporters/ConversationExporter';
import * as GatewaySdk from '@reolink-services/api-gateway';

@Rpc.Controller()
export default class ExportController {

    private readonly _conversationExporter = DI.use(ConversationExporter);

    @GatewaySdk.TunnelApi({
        permissions: $P.Conversation.PREM_GET_CONVERSATION,
        validation: {
            'issueResolved?': 'boolean',
            'category?': ['=other', '=consultation', '=troubleshooting'],
            'emotion?': ['=neutral', '=negative', '=positive', '=extremely_negative'],
            'isPassToAgent?': 'boolean',
            'isCreateTicket?': 'boolean',
            'sopId?': 'string',
            'isWrongIntent?': 'boolean'
        }
    })
    public async exportConversations(
        args: IExportConversationsArgs,
        meta: Rpc.IMetadata
    ): Promise<IExportConversationsResponse> {

        const merchant = GatewaySdk.getMerchantIdFromMetadata(meta, true);
        const createdBy = parseInt(GatewaySdk.getAccountIdFromMetadata(meta, true));

        const taskId = await this._conversationExporter.createConversationTask(
            args,
            merchant,
            createdBy
        );

        _.Async.invokeAsync(
            this._conversationExporter.exportRec.bind(this),
            args,
            merchant,
            taskId,
        );

        return {
            taskId: taskId
        };
    }

}
