import * as Rpc from '@reolink-fx/rpc';
import * as GatewaySdk from '@reolink-services/api-gateway';
import * as DI from '@reolink-fx/di';
import { SopStepManager } from '#/Services/sop/SopStepManager';
import * as $P from '@reolink-services/ai-support.platform-apis';
import { BuiltInTypes as $Types } from '@reolink-fx/typeguard';

@Rpc.Controller()
export default class SopStepController implements $P.Step.IApiSet {

    private readonly _stepManager = DI.use(SopStepManager);

    @GatewaySdk.TunnelApi({
        permissions: $P.Step.PREM_CREATE_STEP,
        validation: {
            'step': 'string(1,512)',
            'options': {
                'articleId?': 'string',
                'description?': 'string'
            }
        }
    })
    public async createStep(
        args: $P.Step.ICreateStepArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse> {
        await this._stepManager.createStep(args, GatewaySdk.getAccountIdFromMetadata(meta, true));
        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Step.PREM_UPDATE_STEP,
        validation: {
            'stepId': $Types.TYPE_SNOWFLAKE_ID,
            'step?': 'string(1,512)',
            'options?': {
                'articleId?': 'string',
                'description?': 'string'
            }
        }
    })
    public async updateStep(
        args: $P.Step.IUpdateStepArgs,
        meta: Rpc.IMetadata
    ): Promise<Rpc.IOkResponse> {
        await this._stepManager.updateStep(
            args.stepId.toString(),
            args,
            GatewaySdk.getAccountIdFromMetadata(meta, true)
        );
        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Step.PREM_DELETE_STEP,
        validation: {
            'stepId': $Types.TYPE_SNOWFLAKE_ID
        }
    })
    public async deleteStep(
        args: $P.Step.IDeleteStepArgs
    ): Promise<Rpc.IOkResponse> {
        await this._stepManager.deleteStep(args.stepId);
        return Rpc.OK_RESPONSE;
    }

    @GatewaySdk.TunnelApi({
        permissions: $P.Step.PREM_GET_STEP,
        validation: {
            'step?': 'string(1,512)',
            ...GatewaySdk.createPageQueryArgsValidation({
                'maxPage': 1000,
                'maxLimit': 100
            })
        }
    })
    public async getStepList(
        args: $P.Step.IGetStepListArgs
    ): Promise<Rpc.IListResponse<$P.Step.IStep>> {
        const [steps, totalRows] = await this._stepManager.getSteps(args);
        return {
            'totalRows': totalRows,
            'items': steps.map(v => ({
                ...v,
                createdAt: parseInt(v.createdAt),
                modifiedAt: parseInt(v.modifiedAt)
            }))
        };
    }
}
