import * as Rpc from '@reolink-fx/rpc';
import * as $I from '@reolink-services/ai-support';
import * as DI from '@reolink-fx/di';
import { ChatSceneManager, EChatScene } from '#/Services/ChatSceneManager';
import { PromptManager } from '#/Services/PromptManager';
import { CHAT_SEARCH_SCENE } from '#/Services/Decls/Chat';
import { AggregationPageManager } from '#/Services/AggregationPageManager';
import { ConversationManager } from '#/Services/ConversationManager';

@Rpc.Controller()
export default class ChatSceneController {

    private readonly _chatSceneMgr = DI.use(ChatSceneManager);

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _aggregationPageMgr = DI.use(AggregationPageManager);

    private readonly _conversationMgr = DI.use(ConversationManager);

    @Rpc.Api({
        'validation': {
            'scene': 'string(1,32)',
            'content': 'string(1,20000)'
        }
    })
    public async chatWithScene(
        args: $I.Chat.IChatWithSceneArgs
    ): Promise<$I.Chat.IChatWithSceneResponse> {

        let prompt = '';

        switch (args.scene) {

            case EChatScene.CLASSIFICATION: {
                prompt = await this._chatSceneMgr.generateTicketClassificationPrompt(args.content);

                // await _.File.writeFile('/home/<USER>/projects/svc-ai-support/prompt_check', prompt);

                const resp = await this._chatSceneMgr.chatWithModelInClaude(prompt);
                return {
                    'response': resp
                };

            }
            case EChatScene.SEQUENT_REPLY: {

                return {
                    'response': await this._chatSceneMgr.sequentReply(args.content, args.otherInfo ?? {})
                };
            }
            case CHAT_SEARCH_SCENE: {

                const productModel = args.otherInfo?.productModel?.toString() ?? '';

                const resp = await this._conversationMgr.createConversation({
                    'content': args.content,
                    'createdAt': Date.now().toString()
                },
                args.otherInfo ?? {}
                );

                const slugs = await this._aggregationPageMgr.detectSlug(args.content, productModel);

                if (!slugs.length) {
                    return {
                        response: JSON.stringify(await this._chatSceneMgr.handleNoSlugsFound({
                            ...args,
                            conversationId: resp.conversationId,
                        }))
                    };
                }

                prompt = (await this._promptManager.generateAggregationPrompt(args.content, {
                    'slugs': slugs,
                    'productModel': productModel
                }))?.prompt ?? '';
                break;
            }
            default: {
                prompt = await this._promptManager.generatePromptByScene(args.scene, args.content);
            }
        }

        const resp = await this._chatSceneMgr.chatWithModel(prompt);

        return {
            'response': resp
        };

    }

}
