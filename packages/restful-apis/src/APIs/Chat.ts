import * as Rpc from '@reolink-fx/rpc';
import type * as GatewaySdk from '@reolink-services/api-gateway';
export interface IChatWithSceneBodyArgs {

    /**
     * 场景 key
     *
     * 取值范围: 1-32 个字符
     */
    scene: string;

    /**
     * 提问内容
     */
    content: string;

    /**
     * 额外参数
     */
    params?: Record<string, unknown>;

}

export interface IChatWithSceneResponse {

    /**
     * 对话id
     *
     * 取值范围：safe_uint
     */
    conversationId: string;
}

/**
 * 表示整个响应对象的结构。
 */
export interface IChatResponse {

    /**
     * 消息对象。
     */
    message: IMessage;

    /**
     * 对话的唯一标识符。
     */
    conversationId: string;

    /**
     * 错误信息symbol。
     */
    error: string;
}

/**
   * 表示响应中的单个消息。
   */
export interface IMessage {

    /**
     * 消息的唯一标识符。
     */
    id: string;

    /**
     * 消息创建的时间戳，单位为毫秒的 Unix 时间戳。
     */
    createdAt: number;

    /**
     * 消息的内容。
     */
    content: IMessageContent;

    /**
     * 消息的状态，例如 "in_progress"。
     */
    status: 'in_progress' | 'finished';

}

/**
   * 表示消息内容的结构。
   */
export interface IMessageContent {

    /**
     * 内容的类型
     */
    contentType: 'text';

    /**
     * 组成内容的部分数组。
     */
    parts: string[];
}

export type TUnresolvedReason = 'too_much_irrelevant_information' | 'not_consistent_with_the_facts'
| 'provided_step_cannot_be_completed' | 'waiting_time_is_too_long' | 'page_interaction' | 'other';

export interface ICreateConversationFeedbackBodyArgs {

    /**
     * 对话id
     *
     * 取值范围：safe_uint
     */
    conversationId: string;

    /**
     * 未解决问题的原因
     * too_much_irrelevant_information: 太多不相关信息   not_consistent_with_the_facts: 与事实不一致
     * provided_step_cannot_be_completed: 无法完成给出的步骤  waiting_time_is_too_long: 等待时间过长  other: 其他
     */
    problemUnresolvedReasons?: TUnresolvedReason[];

    /**
     * 问题是否得到解决
     *
     */
    issueResolved: boolean;

    /**
     * 额外评论
     *
     * 取值范围: 1 - 255 个字符
     */
    additionalComments?: string;

}

export interface IGetChatSceneStatusPathArgs {

    /**
     * 场景 key
     *
     * 取值范围: 1-32 个字符
     */
    scene: string;

}

export interface IGetChatSceneStatusResponse {

    /**
     * 状态
     */
    status: 'active' | 'inactive';

}

export interface IAgentResponse {
    response: string;
    threadId: string;
}

export interface IWebhookMessage {
    app: {
        id: string;
    };
    webhook: {
        id: string;
        version: string;
    };
    events: Array<{
        id: string;
        type: string;
        createdAt: string;
        payload: {
            conversation: {
                id: string;
                type: string;
            };
            message: {
                id: string;
                received: string;
                content: {
                    type: string;
                    text: string;
                };
                author: {
                    type: string;
                    userId: string;
                    displayName: string;
                    user: {
                        id: string;
                    };
                };
                source: {
                    type: string;
                    integrationId: string;
                };
            };
        };
    }>;
}
export interface IApiSet {

    chatWithScene(
        args: GatewaySdk.IRestfulParameter<IChatWithSceneBodyArgs, Record<string, any>>
    ): IChatWithSceneResponse;

    getChatSceneStatus(
        args: GatewaySdk.IRestfulParameter<unknown, IGetChatSceneStatusPathArgs>
    ): IGetChatSceneStatusResponse;

    receiveMessage(
        args: GatewaySdk.IRestfulParameter<IWebhookMessage,  Record<string, unknown>>

    ): Rpc.IOkResponse;

}
