import type * as GatewaySdk from '@reolink-services/api-gateway';
export interface IGetAggregationPageSlugArgs {

    /**
     * 提问内容
     * 取值范围：0 ~ 5000 字符
     */
    content: string;

    /**
     * 产品机型
     * 取值范围：1 ~ 255 字符
     */
    productModel: string;

}

export interface IAggregationPageSlugs {

    /**
     * 聚合页key列表
     */
    slugs: string[];

}

export interface IApiSet {

    getAggregationPageSlug(
        args: GatewaySdk.IRestfulParameter<IGetAggregationPageSlugArgs, Record<string, any>>
    ): IAggregationPageSlugs;

}
