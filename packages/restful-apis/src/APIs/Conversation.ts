import * as Rpc from '@reolink-fx/rpc';
import type * as GatewaySdk from '@reolink-services/api-gateway';

export type TUnresolvedReason = 'too_much_irrelevant_information' | 'not_consistent_with_the_facts'
| 'provided_step_cannot_be_completed' | 'waiting_time_is_too_long' | 'other';

export interface ICreateConversationFeedbackBodyArgs {

    /**
     * 对话id
     *
     * 取值范围：safe_uint
     */
    conversationId: string;

    /**
     * 未解决问题的原因
     * too_much_irrelevant_information: 太多不相关信息   not_consistent_with_the_facts: 与事实不一致
     * provided_step_cannot_be_completed: 无法完成给出的步骤  waiting_time_is_too_long: 等待时间过长  other: 其他
     */
    problemUnresolvedReasons?: TUnresolvedReason[];

    /**
     * 问题是否得到解决
     *
     */
    issueResolved: boolean;

    /**
     * 额外评论
     *
     * 取值范围: 1 - 255 个字符
     */
    additionalComments?: string;

}

export interface IApiSet {
    createConversationFeedback(
        args: GatewaySdk.IRestfulParameter<ICreateConversationFeedbackBodyArgs, Record<string, unknown>>
    ): Rpc.IOkResponse;
}
