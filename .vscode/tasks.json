{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "[Reolink/Service.AISupport] Build (Prefer Local Compiler)",
            "type": "npm",
            "script": "build",
            "problemMatcher": [
                "$tsc-watch"
            ],
            "isBackground": true
        },
        {
            "label": "[Reolink/Service.AISupport] Build (Watching, Prefer Local Compiler)",
            "type": "npm",
            "script": "build-watch",
            "problemMatcher": [
                "$tsc-watch"
            ],
            "isBackground": true
        }
    ]
}