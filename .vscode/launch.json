{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "node",
            "request": "launch",
            "name": "[Reolink/Services.AISupport]: Start Server",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "program": "${workspaceFolder}/node_modules/.bin/reolink",
            "runtimeExecutable": "/root/.nvm/versions/node/v20.17.0/bin/node",
            "args": [
                "run",
                ".",
                "config.yml"
            ],
            "cwd": "${workspaceFolder}/packages/server",
            "outFiles": [
                "${workspaceFolder}/**/*.js"
            ]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "[Reolink/Services.AISupport]: Test",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "program": "${workspaceFolder}/node_modules/.bin/reolink",
            "args": [
                "run",
                ".",
                "packages/test/config.json"
            ],
            "cwd": "${workspaceFolder}/packages/test",
            "outFiles": [
                "${workspaceFolder}/**/*.js"
            ]
        },
        {
            "type": "node",
            "name": "Debug online",
            "request": "attach",
            "remoteRoot": "/data",
            "address": "************",
            "port": 45773,
            "sourceMaps": true,
            "outFiles": [
                "${workspaceFolder}/**/*.js",
                "!**/node_modules/**"
            ],
            "timeout": 10000 
        }
    ]
}